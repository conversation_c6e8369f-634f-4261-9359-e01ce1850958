{"name": "lingxiai-gemini", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed-users.ts"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "drizzle-orm": "^0.36.0", "drizzle-zod": "^0.8.2", "lucide-react": "^0.263.1", "next": "15.3.4", "postgres": "^3.4.7", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/pg": "^8.10.9", "@types/react": "^18", "@types/react-dom": "^18", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.2", "eslint": "^8", "eslint-config-next": "15.3.4", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5"}}