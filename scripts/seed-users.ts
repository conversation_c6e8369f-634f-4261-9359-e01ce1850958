import { createClient } from '@supabase/supabase-js';

// 确保你有这些环境变量
const supabaseUrl = 'https://hgwftsuazcmgemuxxpue.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhnd2Z0c3VhemNtZ2VtdXh4cHVlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyOTgxMCwiZXhwIjoyMDY2NTA1ODEwfQ.1eUQuKfFmBec-HVGPclEnP6iNyZ09IQu82F4wFR4F-s';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// 男性用户数据 (10个)
const maleUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '晓明',
      age: 28,
      gender: 'male',
      location: '上海',
      bio: '程序员，热爱技术和创新，业余时间喜欢弹吉他和看科幻电影。',
      interests: ['编程', '科技', '吉他', '科幻电影', '游戏', '咖啡', '创业'],
    },
    profile: {
      self_description: '我是一名全栈开发工程师，对新技术充满热情。工作之余喜欢弹吉他，觉得音乐能让人放松。我是个理性的人，但也有浪漫的一面。',
      looking_for: '希望找到一个理解我工作的人，最好也是个有自己事业追求的独立女性。我们可以一起成长，互相支持。',
      relationship_goals: '想要建立一个平等、互相尊重的关系，一起为未来努力。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '浩然',
      age: 30,
      gender: 'male',
      location: '杭州',
      bio: '健身教练，热爱运动和健康生活。相信身体和心灵都需要锻炼。',
      interests: ['健身', '跑步', '游泳', '营养学', '户外运动', '瑜伽', '冥想'],
    },
    profile: {
      self_description: '我是一名私人健身教练，热爱运动和健康的生活方式。我相信健康的身体是一切的基础，也喜欢帮助别人变得更健康。性格开朗，喜欢户外活动。',
      looking_for: '希望找到一个同样热爱健康生活的伴侣，我们可以一起运动、一起保持健康的生活习惯。',
      relationship_goals: '想要建立一个健康、积极的关系，一起追求更好的生活品质。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '志伟',
      age: 32,
      gender: 'male',
      location: '广州',
      bio: '创业者，经营一家小型咖啡店。热爱咖啡文化和创业精神。',
      interests: ['咖啡', '创业', '商业', '烘焙', '品酒', '旅行', '摄影'],
    },
    profile: {
      self_description: '我经营着一家精品咖啡店，对咖啡有着深深的热爱。创业虽然辛苦，但我享受这个过程。我是个乐观的人，相信努力就会有回报。',
      looking_for: '希望找到一个理解创业艰辛但依然支持我的人，最好也有自己的事业追求。',
      relationship_goals: '想要找到人生的合伙人，一起为梦想努力。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '俊豪',
      age: 29,
      gender: 'male',
      location: '南京',
      bio: '医生，救死扶伤是我的使命。工作之余喜欢阅读医学文献和跑步。',
      interests: ['医学', '跑步', '阅读', '志愿服务', '健康养生', '科研', '公益'],
    },
    profile: {
      self_description: '我是一名内科医生，工作虽然忙碌但很有意义。我相信健康是最重要的财富，也希望能帮助更多的人。性格稳重，有责任心。',
      looking_for: '希望找到一个善良、理解医生工作特殊性的人。我们可以一起做公益，帮助需要帮助的人。',
      relationship_goals: '想要建立一个稳定、相互支持的家庭。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '天宇',
      age: 31,
      gender: 'male',
      location: '重庆',
      bio: '摄影师，用镜头记录世界的美好。热爱艺术和创作。',
      interests: ['摄影', '艺术', '电影', '旅行', '后期制作', '展览', '创作'],
    },
    profile: {
      self_description: '我是一名自由摄影师，主要拍摄人像和风景。我相信每个瞬间都有它独特的美，我的工作就是捕捉这些美好。我喜欢和有趣的人交流，从他们身上获得创作灵感。',
      looking_for: '希望遇到一个有艺术气质、理解创作者心境的人。我们可以一起去看展览，一起创作。',
      relationship_goals: '想要找到我的缪斯，一起创造艺术作品。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '晨阳',
      age: 27,
      gender: 'male',
      location: '武汉',
      bio: '建筑师，设计城市的未来。热爱设计和建筑美学。',
      interests: ['建筑设计', '城市规划', '素描', '模型制作', '历史建筑', '旅行', '艺术'],
    },
    profile: {
      self_description: '我是一名建筑师，专注于可持续建筑设计。我相信好的建筑能改善人们的生活质量。我喜欢手绘设计图，也喜欢研究各地的历史建筑。',
      looking_for: '希望遇到一个有审美品味、理解设计工作的人。我们可以一起去看建筑展，一起旅行欣赏世界各地的建筑。',
      relationship_goals: '想要建造一个温馨的家，和爱人一起设计我们的未来。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '逸凡',
      age: 26,
      gender: 'male',
      location: '厦门',
      bio: '音乐制作人，用音符编织梦想。热爱各种音乐风格。',
      interests: ['音乐制作', '吉他', '钢琴', '作曲', '录音', '演出', '音乐节'],
    },
    profile: {
      self_description: '我是一名独立音乐制作人，主要制作流行和民谣音乐。我相信音乐是世界通用的语言，能够触动人心。我经常在livehouse演出，享受和观众的互动。',
      looking_for: '希望找到一个热爱音乐、理解艺术创作的人。最好也会一些乐器，我们可以一起创作音乐。',
      relationship_goals: '想要找到能和我一起创作人生乐章的伴侣。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '子豪',
      age: 33,
      gender: 'male',
      location: '天津',
      bio: '律师，为正义而战。相信法律的力量，也相信人性的善良。',
      interests: ['法律', '阅读', '辩论', '历史', '政治学', '公益法律', '写作'],
    },
    profile: {
      self_description: '我是一名执业律师，主要处理民事和商事案件。我相信每个人都应该得到公正的对待，所以我也经常做一些公益法律援助。我喜欢读书和思考，性格比较严谨但也很幽默。',
      looking_for: '希望遇到一个有正义感、理解我工作性质的人。我们可以一起讨论社会问题，一起为公正而努力。',
      relationship_goals: '想要建立一个基于相互尊重和理解的长期关系。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '瑞斌',
      age: 35,
      gender: 'male',
      location: '苏州',
      bio: '厨师，用美食传递爱意。热爱烹饪和美食文化。',
      interests: ['烹饪', '美食', '食材研究', '餐厅经营', '红酒', '旅行', '文化'],
    },
    profile: {
      self_description: '我是一名专业厨师，经营着一家小餐厅。我相信美食能够拉近人与人之间的距离，每一道菜都承载着我的用心。我喜欢研究各地的美食文化，也喜欢为爱的人下厨。',
      looking_for: '希望找到一个懂得欣赏美食、愿意和我一起品尝人生百味的人。最好也喜欢烹饪，我们可以一起创造美味。',
      relationship_goals: '想要和爱人一起经营我们的小餐厅，创造属于我们的美食故事。',
    }
  }
];

// 女性用户数据 (20个)
const femaleUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '小雨',
      age: 26,
      gender: 'female',
      location: '北京',
      bio: '喜欢阅读和旅行，寻找有趣的灵魂。热爱文学，相信每一次旅行都是心灵的洗礼。',
      interests: ['阅读', '旅行', '摄影', '咖啡', '电影', '瑜伽', '音乐'],
    },
    profile: {
      self_description: '我是一个热爱生活的文艺青年，喜欢在安静的午后读一本好书，也喜欢背着相机去探索未知的地方。我相信每个人都有自己独特的故事，希望能遇到一个愿意和我分享故事的人。',
      looking_for: '希望找到一个有趣、善良、有自己想法的人。年龄不是问题，重要的是心灵的契合。最好也喜欢旅行和阅读，我们可以一起探索世界。',
      relationship_goals: '寻找一段认真的感情，希望能从朋友开始，慢慢了解彼此，最终走向婚姻。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '梦琪',
      age: 24,
      gender: 'female',
      location: '深圳',
      bio: '设计师，喜欢一切美好的事物。热爱艺术、美食和慢生活。',
      interests: ['设计', '艺术', '美食', '手工', '花艺', '茶道', '绘画'],
    },
    profile: {
      self_description: '我是一名UI/UX设计师，对美有着天生的敏感。喜欢手工制作，周末经常去花市买花回来插花。我相信生活需要仪式感，喜欢把平凡的日子过得有趣。',
      looking_for: '希望遇到一个有生活品味的人，我们可以一起品茶、看展、做手工。最重要的是要有耐心和温柔。',
      relationship_goals: '想要一段温馨的恋爱关系，两个人一起创造美好的回忆。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '雨婷',
      age: 27,
      gender: 'female',
      location: '成都',
      bio: '心理咨询师，喜欢倾听和帮助他人。热爱心理学和人文学科。',
      interests: ['心理学', '阅读', '写作', '冥想', '心理咨询', '人文学科', '哲学'],
    },
    profile: {
      self_description: '我是一名心理咨询师，喜欢研究人的内心世界。我是个很好的倾听者，也喜欢深度的交流。相信每个人都值得被理解和关爱。',
      looking_for: '希望遇到一个情感成熟、愿意深度交流的人。我们可以互相理解，共同成长。',
      relationship_goals: '寻找一段深度的精神连接，希望能够真正理解彼此。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '思思',
      age: 25,
      gender: 'female',
      location: '西安',
      bio: '古典舞老师，热爱传统文化和艺术。优雅而有内涵。',
      interests: ['古典舞', '传统文化', '书法', '古筝', '茶艺', '汉服', '诗词'],
    },
    profile: {
      self_description: '我是一名古典舞老师，从小学习舞蹈，对传统文化有着深厚的感情。我喜欢穿汉服，练书法，觉得这些能让人内心平静。',
      looking_for: '希望遇到一个有文化底蕴、尊重传统的人。我们可以一起品茶论道，欣赏古典艺术。',
      relationship_goals: '想要一段如诗如画的恋情，两个人一起传承和发扬传统文化。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '小涵',
      age: 23,
      gender: 'female',
      location: '青岛',
      bio: '海洋生物学研究生，热爱大海和自然。梦想是保护海洋环境。',
      interests: ['海洋生物', '环保', '潜水', '摄影', '自然纪录片', '科研', '旅行'],
    },
    profile: {
      self_description: '我正在攻读海洋生物学硕士学位，对海洋世界充满好奇。我经常去潜水，用相机记录海底的美丽。我是个环保主义者，希望为保护地球做贡献。',
      looking_for: '希望遇到一个同样关心环境、热爱自然的人。我们可以一起去看海，一起为环保事业努力。',
      relationship_goals: '想要找到一个志同道合的伴侣，一起为理想奋斗。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '琳琳',
      age: 28,
      gender: 'female',
      location: '杭州',
      bio: '瑜伽教练，追求身心平衡。热爱健康生活和精神修行。',
      interests: ['瑜伽', '冥想', '健康饮食', '精油', '自然疗法', '旅行', '读书'],
    },
    profile: {
      self_description: '我是一名瑜伽教练，通过瑜伽找到了内心的平静。我相信身体和心灵的健康同样重要，喜欢帮助别人找到内在的力量。我热爱自然，经常去山里冥想。',
      looking_for: '希望遇到一个同样注重精神成长、热爱健康生活的人。我们可以一起练瑜伽，一起探索内在的世界。',
      relationship_goals: '想要建立一段平衡和谐的关系，两个人一起成长，一起修行。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '婉婉',
      age: 29,
      gender: 'female',
      location: '南京',
      bio: '护士，用爱心照顾他人。温柔善良，相信爱能治愈一切。',
      interests: ['医护工作', '志愿服务', '烘焙', '园艺', '阅读', '音乐', '公益'],
    },
    profile: {
      self_description: '我是一名ICU护士，虽然工作很辛苦，但能帮助病人康复让我很有成就感。我性格温和，喜欢烘焙和园艺，觉得这些能让人心情平静。我相信善良和爱心是最珍贵的品质。',
      looking_for: '希望找到一个善良、有责任心的人，能够理解我的工作。我们可以一起做公益，一起传递爱心。',
      relationship_goals: '想要建立一个充满爱和温暖的家庭，互相照顾，共同成长。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '希希',
      age: 22,
      gender: 'female',
      location: '大连',
      bio: '大学生，学习国际贸易。热爱语言学习和跨文化交流。',
      interests: ['语言学习', '国际文化', '旅行', '摄影', '美食', '音乐', '电影'],
    },
    profile: {
      self_description: '我是一名大四学生，主修国际贸易，会说四种语言。我对不同的文化很感兴趣，梦想是环游世界。我性格开朗活泼，喜欢结交来自不同国家的朋友。',
      looking_for: '希望遇到一个有国际视野、喜欢旅行的人。年龄不是问题，重要的是有共同的兴趣和价值观。',
      relationship_goals: '想要找到一个可以一起探索世界的伴侣，一起体验不同的文化。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '苗苗',
      age: 26,
      gender: 'female',
      location: '长沙',
      bio: '幼儿园老师，喜欢孩子和教育。相信每个孩子都是天使。',
      interests: ['幼儿教育', '儿童心理学', '手工制作', '绘画', '音乐', '舞蹈', '故事'],
    },
    profile: {
      self_description: '我是一名幼儿园老师，每天和小朋友们在一起让我很快乐。我有耐心，喜欢用创意的方式教育孩子。我相信爱和陪伴是给孩子最好的礼物。',
      looking_for: '希望遇到一个同样喜欢孩子、有爱心和耐心的人。我们可以一起为孩子们创造美好的童年。',
      relationship_goals: '想要建立一个温馨的家庭，一起养育可爱的孩子。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '菲菲',
      age: 30,
      gender: 'female',
      location: '武汉',
      bio: '市场营销经理，善于沟通和策划。热爱挑战和创新。',
      interests: ['市场营销', '品牌策划', '演讲', '网球', '红酒', '旅行', '时尚'],
    },
    profile: {
      self_description: '我是一名市场营销经理，负责品牌推广和活动策划。我喜欢挑战，享受在工作中解决问题的成就感。我性格外向，善于沟通，也很有组织能力。',
      looking_for: '希望找到一个有事业心、能够理解我工作节奏的人。我们可以互相支持，一起追求事业和生活的平衡。',
      relationship_goals: '想要找到一个可以并肩作战的伴侣，一起创造精彩的人生。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '露露',
      age: 24,
      gender: 'female',
      location: '厦门',
      bio: '服装设计师，追求时尚与艺术的完美结合。热爱创作和美学。',
      interests: ['服装设计', '时尚', '绘画', '面料研究', '时装周', '艺术', '摄影'],
    },
    profile: {
      self_description: '我是一名服装设计师，专注于可持续时尚设计。我相信服装不仅是穿着，更是自我表达的方式。我喜欢从生活中汲取灵感，创造独特的设计。',
      looking_for: '希望遇到一个有艺术品味、理解创作过程的人。我们可以一起去看时装展，一起探讨美学。',
      relationship_goals: '想要找到一个能够欣赏我作品、支持我创作的伴侣。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '琪琪',
      age: 27,
      gender: 'female',
      location: '天津',
      bio: '翻译，精通多种语言。热爱文学和跨文化交流。',
      interests: ['翻译', '文学', '语言学', '阅读', '写作', '旅行', '文化研究'],
    },
    profile: {
      self_description: '我是一名自由翻译，主要从事文学翻译工作。我精通英语、法语和日语，喜欢在不同语言之间架起沟通的桥梁。我热爱阅读，相信文字有改变世界的力量。',
      looking_for: '希望遇到一个同样热爱文学、有深度思考能力的人。我们可以一起读书，一起讨论文学作品。',
      relationship_goals: '想要建立一段充满智慧和深度的关系，两个人一起在精神世界中遨游。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '娜娜',
      age: 25,
      gender: 'female',
      location: '重庆',
      bio: '美食博主，探索各地美食文化。用味蕾记录生活的美好。',
      interests: ['美食', '烹饪', '摄影', '旅行', '文化', '写作', '品酒'],
    },
    profile: {
      self_description: '我是一名美食博主，喜欢探索各地的特色美食和文化。我相信美食是了解一个地方最好的方式，每一道菜都有它的故事。我喜欢用镜头和文字记录美食的魅力。',
      looking_for: '希望找到一个同样热爱美食、愿意和我一起品尝世界各地美味的人。我们可以一起做饭，一起探索美食文化。',
      relationship_goals: '想要找到一个可以一起品味人生的伴侣，一起创造美食回忆。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '圆圆',
      age: 28,
      gender: 'female',
      location: '沈阳',
      bio: '会计师，细心负责。热爱数字和逻辑，也喜欢音乐和艺术。',
      interests: ['财务管理', '投资理财', '钢琴', '古典音乐', '阅读', '旅行', '美术'],
    },
    profile: {
      self_description: '我是一名注册会计师，工作中很细心严谨，但生活中我也有感性的一面。我从小学钢琴，喜欢古典音乐，觉得音乐能让人内心平静。我相信理性和感性可以很好地结合。',
      looking_for: '希望遇到一个有责任心、理解我工作性质的人。最好也有一些艺术爱好，我们可以一起欣赏音乐和艺术。',
      relationship_goals: '想要建立一个稳定而有趣的关系，两个人一起规划美好的未来。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '潇潇',
      age: 31,
      gender: 'female',
      location: '西宁',
      bio: '环保工程师，致力于保护环境。热爱自然和户外运动。',
      interests: ['环保', '户外运动', '登山', '摄影', '自然保护', '科研', '旅行'],
    },
    profile: {
      self_description: '我是一名环保工程师，专注于水污染治理项目。我热爱大自然，经常去登山和徒步，用相机记录自然的美丽。我相信每个人都有责任保护我们的地球。',
      looking_for: '希望遇到一个同样关心环境、热爱户外运动的人。我们可以一起去登山，一起为环保事业努力。',
      relationship_goals: '想要找到一个志同道合的伴侣，一起为保护地球贡献力量。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '晶晶',
      age: 26,
      gender: 'female',
      location: '兰州',
      bio: '珠宝设计师，用宝石诠释美丽。热爱艺术和手工创作。',
      interests: ['珠宝设计', '宝石学', '手工制作', '艺术', '时尚', '收藏', '旅行'],
    },
    profile: {
      self_description: '我是一名珠宝设计师，专门设计定制珠宝。我相信每一件珠宝都应该有它独特的故事和意义。我喜欢研究各种宝石，也喜欢手工制作一些小饰品。',
      looking_for: '希望遇到一个有艺术品味、欣赏手工艺术的人。我们可以一起去看珠宝展，一起收藏美丽的东西。',
      relationship_goals: '想要找到一个能够欣赏美、理解艺术的伴侣，一起创造美丽的回忆。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '萌萌',
      age: 23,
      gender: 'female',
      location: '哈尔滨',
      bio: '动画师，用画笔创造奇幻世界。热爱动漫和创意设计。',
      interests: ['动画制作', '插画', '漫画', '游戏设计', 'cosplay', '手办', '创意'],
    },
    profile: {
      self_description: '我是一名2D动画师，主要制作原创动画短片。我从小就喜欢动漫，梦想是创作出能够感动人心的作品。我性格比较宅，但也很有创意和想象力。',
      looking_for: '希望遇到一个同样热爱动漫、理解创作者心境的人。我们可以一起看动漫，一起讨论剧情和角色。',
      relationship_goals: '想要找到一个能够理解我的创作热情、支持我梦想的伴侣。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '婷婷',
      age: 29,
      gender: 'female',
      location: '石家庄',
      bio: '律师助理，正在准备司法考试。热爱法律和正义事业。',
      interests: ['法律', '阅读', '辩论', '写作', '社会公益', '历史', '政治'],
    },
    profile: {
      self_description: '我目前是一名律师助理，正在努力准备司法考试，希望成为一名执业律师。我相信法律是维护社会公正的重要工具，希望能为需要帮助的人提供法律援助。',
      looking_for: '希望遇到一个有正义感、支持我事业发展的人。我们可以一起讨论社会问题，一起为公正而努力。',
      relationship_goals: '想要建立一个相互支持、共同成长的关系，一起为理想奋斗。',
    }
  }
];

// 合并所有用户数据
const fakeUsers = [...maleUsers, ...femaleUsers];

async function seedUsers() {
    password: 'password123',
    userData: {
      name: '浩然',
      age: 30,
      gender: 'male',
      location: '杭州',
      bio: '健身教练，热爱运动和健康生活。相信身体和心灵都需要锻炼。',
      interests: ['健身', '跑步', '游泳', '营养学', '户外运动', '瑜伽', '冥想'],
    },
    profile: {
      self_description: '我是一名私人健身教练，热爱运动和健康的生活方式。我相信健康的身体是一切的基础，也喜欢帮助别人变得更健康。性格开朗，喜欢户外活动。',
      looking_for: '希望找到一个同样热爱健康生活的伴侣，我们可以一起运动、一起保持健康的生活习惯。',
      relationship_goals: '想要建立一个健康、积极的关系，一起追求更好的生活品质。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '雨婷',
      age: 27,
      gender: 'female',
      location: '成都',
      bio: '心理咨询师，喜欢倾听和帮助他人。热爱心理学和人文学科。',
      interests: ['心理学', '阅读', '写作', '冥想', '心理咨询', '人文学科', '哲学'],
    },
    profile: {
      self_description: '我是一名心理咨询师，喜欢研究人的内心世界。我是个很好的倾听者，也喜欢深度的交流。相信每个人都值得被理解和关爱。',
      looking_for: '希望遇到一个情感成熟、愿意深度交流的人。我们可以互相理解，共同成长。',
      relationship_goals: '寻找一段深度的精神连接，希望能够真正理解彼此。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '志伟',
      age: 32,
      gender: 'male',
      location: '广州',
      bio: '创业者，经营一家小型咖啡店。热爱咖啡文化和创业精神。',
      interests: ['咖啡', '创业', '商业', '烘焙', '品酒', '旅行', '摄影'],
    },
    profile: {
      self_description: '我经营着一家精品咖啡店，对咖啡有着深深的热爱。创业虽然辛苦，但我享受这个过程。我是个乐观的人，相信努力就会有回报。',
      looking_for: '希望找到一个理解创业艰辛但依然支持我的人，最好也有自己的事业追求。',
      relationship_goals: '想要找到人生的合伙人，一起为梦想努力。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '思思',
      age: 25,
      gender: 'female',
      location: '西安',
      bio: '古典舞老师，热爱传统文化和艺术。优雅而有内涵。',
      interests: ['古典舞', '传统文化', '书法', '古筝', '茶艺', '汉服', '诗词'],
    },
    profile: {
      self_description: '我是一名古典舞老师，从小学习舞蹈，对传统文化有着深厚的感情。我喜欢穿汉服，练书法，觉得这些能让人内心平静。',
      looking_for: '希望遇到一个有文化底蕴、尊重传统的人。我们可以一起品茶论道，欣赏古典艺术。',
      relationship_goals: '想要一段如诗如画的恋情，两个人一起传承和发扬传统文化。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '俊豪',
      age: 29,
      gender: 'male',
      location: '南京',
      bio: '医生，救死扶伤是我的使命。工作之余喜欢阅读医学文献和跑步。',
      interests: ['医学', '跑步', '阅读', '志愿服务', '健康养生', '科研', '公益'],
    },
    profile: {
      self_description: '我是一名内科医生，工作虽然忙碌但很有意义。我相信健康是最重要的财富，也希望能帮助更多的人。性格稳重，有责任心。',
      looking_for: '希望找到一个善良、理解医生工作特殊性的人。我们可以一起做公益，帮助需要帮助的人。',
      relationship_goals: '想要建立一个稳定、相互支持的家庭。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '小涵',
      age: 23,
      gender: 'female',
      location: '青岛',
      bio: '海洋生物学研究生，热爱大海和自然。梦想是保护海洋环境。',
      interests: ['海洋生物', '环保', '潜水', '摄影', '自然纪录片', '科研', '旅行'],
    },
    profile: {
      self_description: '我正在攻读海洋生物学硕士学位，对海洋世界充满好奇。我经常去潜水，用相机记录海底的美丽。我是个环保主义者，希望为保护地球做贡献。',
      looking_for: '希望遇到一个同样关心环境、热爱自然的人。我们可以一起去看海，一起为环保事业努力。',
      relationship_goals: '想要找到一个志同道合的伴侣，一起为理想奋斗。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '天宇',
      age: 31,
      gender: 'male',
      location: '重庆',
      bio: '摄影师，用镜头记录世界的美好。热爱艺术和创作。',
      interests: ['摄影', '艺术', '电影', '旅行', '后期制作', '展览', '创作'],
    },
    profile: {
      self_description: '我是一名自由摄影师，主要拍摄人像和风景。我相信每个瞬间都有它独特的美，我的工作就是捕捉这些美好。我喜欢和有趣的人交流，从他们身上获得创作灵感。',
      looking_for: '希望遇到一个有艺术气质、理解创作者心境的人。我们可以一起去看展览，一起创作。',
      relationship_goals: '想要找到我的缪斯，一起创造艺术作品。',
    }
  }
];

async function seedUsers() {
  console.log('开始创建假用户...');

  for (const fakeUser of fakeUsers) {
    try {
      // 1. 在 Supabase Auth 中创建用户
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: fakeUser.email,
        password: fakeUser.password,
        email_confirm: true, // 自动确认邮箱
      });

      if (authError) {
        console.error(`创建用户 ${fakeUser.email} 失败:`, authError);
        continue;
      }

      const userId = authData.user.id;
      console.log(`✅ 创建 Auth 用户: ${fakeUser.email} (${userId})`);

      // 2. 在我们的 users 表中创建记录
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: fakeUser.email,
          ...fakeUser.userData,
        })
        .select()
        .single();

      if (userError) {
        console.error(`创建用户记录失败:`, userError);
        continue;
      }

      console.log(`✅ 创建 Users 记录: ${userData.name}`);

      // 3. 创建用户资料
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          ...fakeUser.profile,
        });

      if (profileError) {
        console.error(`创建用户资料失败:`, profileError);
        continue;
      }

      console.log(`✅ 创建 Profile 记录: ${userData.name}`);

    } catch (error) {
      console.error(`处理用户 ${fakeUser.email} 时出错:`, error);
    }
  }

  console.log('🎉 假用户创建完成！');
}

// 运行脚本
if (require.main === module) {
  seedUsers().catch(console.error);
}

export { seedUsers };
