import { createClient } from '@supabase/supabase-js';
import { db } from '../src/lib/db';
import { users, userProfiles } from '../src/lib/db/schema';

// 确保你有这些环境变量
const supabaseUrl = 'https://hgwftsuazcmgemuxxpue.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const fakeUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '小雨',
      age: 26,
      gender: 'female',
      location: '北京',
      bio: '喜欢阅读和旅行，寻找有趣的灵魂。热爱文学，相信每一次旅行都是心灵的洗礼。',
      interests: ['阅读', '旅行', '摄影', '咖啡', '电影', '瑜伽', '音乐'],
    },
    profile: {
      selfDescription: '我是一个热爱生活的文艺青年，喜欢在安静的午后读一本好书，也喜欢背着相机去探索未知的地方。我相信每个人都有自己独特的故事，希望能遇到一个愿意和我分享故事的人。',
      lookingFor: '希望找到一个有趣、善良、有自己想法的人。年龄不是问题，重要的是心灵的契合。最好也喜欢旅行和阅读，我们可以一起探索世界。',
      relationshipGoals: '寻找一段认真的感情，希望能从朋友开始，慢慢了解彼此，最终走向婚姻。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '晓明',
      age: 28,
      gender: 'male',
      location: '上海',
      bio: '程序员，热爱技术和创新，业余时间喜欢弹吉他和看科幻电影。',
      interests: ['编程', '科技', '吉他', '科幻电影', '游戏', '咖啡', '创业'],
    },
    profile: {
      selfDescription: '我是一名全栈开发工程师，对新技术充满热情。工作之余喜欢弹吉他，觉得音乐能让人放松。我是个理性的人，但也有浪漫的一面。',
      lookingFor: '希望找到一个理解我工作的人，最好也是个有自己事业追求的独立女性。我们可以一起成长，互相支持。',
      relationshipGoals: '想要建立一个平等、互相尊重的关系，一起为未来努力。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '梦琪',
      age: 24,
      gender: 'female',
      location: '深圳',
      bio: '设计师，喜欢一切美好的事物。热爱艺术、美食和慢生活。',
      interests: ['设计', '艺术', '美食', '手工', '花艺', '茶道', '绘画'],
    },
    profile: {
      selfDescription: '我是一名UI/UX设计师，对美有着天生的敏感。喜欢手工制作，周末经常去花市买花回来插花。我相信生活需要仪式感，喜欢把平凡的日子过得有趣。',
      lookingFor: '希望遇到一个有生活品味的人，我们可以一起品茶、看展、做手工。最重要的是要有耐心和温柔。',
      relationshipGoals: '想要一段温馨的恋爱关系，两个人一起创造美好的回忆。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '浩然',
      age: 30,
      gender: 'male',
      location: '杭州',
      bio: '健身教练，热爱运动和健康生活。相信身体和心灵都需要锻炼。',
      interests: ['健身', '跑步', '游泳', '营养学', '户外运动', '瑜伽', '冥想'],
    },
    profile: {
      selfDescription: '我是一名私人健身教练，热爱运动和健康的生活方式。我相信健康的身体是一切的基础，也喜欢帮助别人变得更健康。性格开朗，喜欢户外活动。',
      lookingFor: '希望找到一个同样热爱健康生活的伴侣，我们可以一起运动、一起保持健康的生活习惯。',
      relationshipGoals: '想要建立一个健康、积极的关系，一起追求更好的生活品质。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '雨婷',
      age: 27,
      gender: 'female',
      location: '成都',
      bio: '心理咨询师，喜欢倾听和帮助他人。热爱心理学和人文学科。',
      interests: ['心理学', '阅读', '写作', '冥想', '心理咨询', '人文学科', '哲学'],
    },
    profile: {
      selfDescription: '我是一名心理咨询师，喜欢研究人的内心世界。我是个很好的倾听者，也喜欢深度的交流。相信每个人都值得被理解和关爱。',
      lookingFor: '希望遇到一个情感成熟、愿意深度交流的人。我们可以互相理解，共同成长。',
      relationshipGoals: '寻找一段深度的精神连接，希望能够真正理解彼此。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '志伟',
      age: 32,
      gender: 'male',
      location: '广州',
      bio: '创业者，经营一家小型咖啡店。热爱咖啡文化和创业精神。',
      interests: ['咖啡', '创业', '商业', '烘焙', '品酒', '旅行', '摄影'],
    },
    profile: {
      selfDescription: '我经营着一家精品咖啡店，对咖啡有着深深的热爱。创业虽然辛苦，但我享受这个过程。我是个乐观的人，相信努力就会有回报。',
      lookingFor: '希望找到一个理解创业艰辛但依然支持我的人，最好也有自己的事业追求。',
      relationshipGoals: '想要找到人生的合伙人，一起为梦想努力。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '思思',
      age: 25,
      gender: 'female',
      location: '西安',
      bio: '古典舞老师，热爱传统文化和艺术。优雅而有内涵。',
      interests: ['古典舞', '传统文化', '书法', '古筝', '茶艺', '汉服', '诗词'],
    },
    profile: {
      selfDescription: '我是一名古典舞老师，从小学习舞蹈，对传统文化有着深厚的感情。我喜欢穿汉服，练书法，觉得这些能让人内心平静。',
      lookingFor: '希望遇到一个有文化底蕴、尊重传统的人。我们可以一起品茶论道，欣赏古典艺术。',
      relationshipGoals: '想要一段如诗如画的恋情，两个人一起传承和发扬传统文化。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '俊豪',
      age: 29,
      gender: 'male',
      location: '南京',
      bio: '医生，救死扶伤是我的使命。工作之余喜欢阅读医学文献和跑步。',
      interests: ['医学', '跑步', '阅读', '志愿服务', '健康养生', '科研', '公益'],
    },
    profile: {
      selfDescription: '我是一名内科医生，工作虽然忙碌但很有意义。我相信健康是最重要的财富，也希望能帮助更多的人。性格稳重，有责任心。',
      lookingFor: '希望找到一个善良、理解医生工作特殊性的人。我们可以一起做公益，帮助需要帮助的人。',
      relationshipGoals: '想要建立一个稳定、相互支持的家庭。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '小涵',
      age: 23,
      gender: 'female',
      location: '青岛',
      bio: '海洋生物学研究生，热爱大海和自然。梦想是保护海洋环境。',
      interests: ['海洋生物', '环保', '潜水', '摄影', '自然纪录片', '科研', '旅行'],
    },
    profile: {
      selfDescription: '我正在攻读海洋生物学硕士学位，对海洋世界充满好奇。我经常去潜水，用相机记录海底的美丽。我是个环保主义者，希望为保护地球做贡献。',
      lookingFor: '希望遇到一个同样关心环境、热爱自然的人。我们可以一起去看海，一起为环保事业努力。',
      relationshipGoals: '想要找到一个志同道合的伴侣，一起为理想奋斗。',
    }
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    userData: {
      name: '天宇',
      age: 31,
      gender: 'male',
      location: '重庆',
      bio: '摄影师，用镜头记录世界的美好。热爱艺术和创作。',
      interests: ['摄影', '艺术', '电影', '旅行', '后期制作', '展览', '创作'],
    },
    profile: {
      selfDescription: '我是一名自由摄影师，主要拍摄人像和风景。我相信每个瞬间都有它独特的美，我的工作就是捕捉这些美好。我喜欢和有趣的人交流，从他们身上获得创作灵感。',
      lookingFor: '希望遇到一个有艺术气质、理解创作者心境的人。我们可以一起去看展览，一起创作。',
      relationshipGoals: '想要找到我的缪斯，一起创造艺术作品。',
    }
  }
];

async function seedUsers() {
  console.log('开始创建假用户...');

  for (const fakeUser of fakeUsers) {
    try {
      // 1. 在 Supabase Auth 中创建用户
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: fakeUser.email,
        password: fakeUser.password,
        email_confirm: true, // 自动确认邮箱
      });

      if (authError) {
        console.error(`创建用户 ${fakeUser.email} 失败:`, authError);
        continue;
      }

      const userId = authData.user.id;
      console.log(`✅ 创建 Auth 用户: ${fakeUser.email} (${userId})`);

      // 2. 在我们的 users 表中创建记录
      const [user] = await db.insert(users).values({
        id: userId,
        email: fakeUser.email,
        ...fakeUser.userData,
      }).returning();

      console.log(`✅ 创建 Users 记录: ${user.name}`);

      // 3. 创建用户资料
      const [profile] = await db.insert(userProfiles).values({
        userId: userId,
        ...fakeUser.profile,
      }).returning();

      console.log(`✅ 创建 Profile 记录: ${user.name}`);

    } catch (error) {
      console.error(`处理用户 ${fakeUser.email} 时出错:`, error);
    }
  }

  console.log('🎉 假用户创建完成！');
}

// 运行脚本
if (require.main === module) {
  seedUsers().catch(console.error);
}

export { seedUsers };
