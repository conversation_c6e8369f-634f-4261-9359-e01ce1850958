'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, ThumbsUp, ThumbsDown } from 'lucide-react';

interface ConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  match: any;
  onFeedback: (matchId: string, feedback: any) => void;
}

export function ConversationModal({ isOpen, onClose, match, onFeedback }: ConversationModalProps) {
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  const conversation = match?.conversationSimulation?.conversation || [];
  const analysis = match?.conversationSimulation?.analysis || {};

  const handleFeedback = (type: 'accurate' | 'inaccurate', rating?: number) => {
    const feedback = {
      feedbackType: type,
      rating: rating || selectedRating,
      aspectRated: 'conversation',
      feedbackText: `用户对模拟对话的反馈：${type === 'accurate' ? '准确' : '不准确'}`
    };

    onFeedback(match.id, feedback);
    setFeedbackSubmitted(true);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI 模拟对话</DialogTitle>
          <DialogDescription>
            基于双方资料生成的模拟对话场景
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Conversation */}
          <div className="space-y-3">
            <h3 className="font-medium">模拟对话</h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3 max-h-60 overflow-y-auto">
              {conversation.map((message: any, index: number) => (
                <div
                  key={index}
                  className={`flex ${message.speaker === 'user1' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] p-3 rounded-lg ${
                      message.speaker === 'user1'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white border'
                    }`}
                  >
                    <div className="text-xs opacity-70 mb-1">
                      {message.speaker === 'user1' ? '你' : match.otherUser?.name || '对方'}
                    </div>
                    <div className="text-sm">{message.message}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Analysis */}
          {analysis && (
            <div className="space-y-4">
              <h3 className="font-medium">AI 分析结果</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">对话流畅度</span>
                    <span className={`text-sm font-medium ${getScoreColor(analysis.conversationFlow || 0)}`}>
                      {analysis.conversationFlow || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">价值观契合</span>
                    <span className={`text-sm font-medium ${getScoreColor(analysis.valueAlignment || 0)}`}>
                      {analysis.valueAlignment || 0}%
                    </span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">沟通匹配度</span>
                    <span className={`text-sm font-medium ${getScoreColor(analysis.communicationMatch || 0)}`}>
                      {analysis.communicationMatch || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">整体兼容性</span>
                    <span className={`text-sm font-medium ${getScoreColor(analysis.overallCompatibility || 0)}`}>
                      {analysis.overallCompatibility || 0}%
                    </span>
                  </div>
                </div>
              </div>

              {analysis.commonTopics && analysis.commonTopics.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">共同话题</h4>
                  <div className="flex flex-wrap gap-1">
                    {analysis.commonTopics.map((topic: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {topic}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {analysis.potentialConflicts && analysis.potentialConflicts.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">潜在冲突点</h4>
                  <div className="flex flex-wrap gap-1">
                    {analysis.potentialConflicts.map((conflict: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {conflict}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Feedback Section */}
          <div className="border-t pt-4">
            <h3 className="font-medium mb-3">这个模拟对话准确吗？</h3>
            
            {!feedbackSubmitted ? (
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm">准确度评分：</span>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        onClick={() => setSelectedRating(rating)}
                        className={`p-1 ${
                          selectedRating && selectedRating >= rating
                            ? 'text-yellow-500'
                            : 'text-gray-300'
                        }`}
                      >
                        <Star className="w-4 h-4 fill-current" />
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback('accurate')}
                    className="flex items-center gap-2"
                  >
                    <ThumbsUp className="w-4 h-4" />
                    准确
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFeedback('inaccurate')}
                    className="flex items-center gap-2"
                  >
                    <ThumbsDown className="w-4 h-4" />
                    不准确
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-green-600 text-sm">
                感谢您的反馈！这将帮助我们改进AI分析的准确性。
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
