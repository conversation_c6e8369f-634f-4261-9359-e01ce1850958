'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, X, Eye, Sparkles } from 'lucide-react';
import Link from 'next/link';

interface MatchCardProps {
  match: any;
  onLike: (matchId: string) => void;
  onPass: (matchId: string) => void;
}

export function MatchCard({ match, onLike, onPass }: MatchCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  const otherUser = match.otherUser || match.user2;
  const compatibilityScore = match.compatibilityScore || 0;

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return '高度匹配';
    if (score >= 60) return '中等匹配';
    return '低度匹配';
  };

  return (
    <Card className="w-full max-w-md mx-auto hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50">
      <CardHeader className="pb-4 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-100 to-blue-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>

        <div className="flex justify-between items-start relative z-10">
          <div className="flex items-center gap-3">
            <div className="w-14 h-14 bg-gradient-to-br from-pink-500 to-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
              {otherUser?.name?.charAt(0)}
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-800">{otherUser?.name}</CardTitle>
              <CardDescription className="text-gray-600 font-medium">
                {otherUser?.age}岁 · {otherUser?.location}
              </CardDescription>
            </div>
          </div>
          <div className="text-right">
            <div className={`text-3xl font-bold ${getScoreColor(compatibilityScore)} drop-shadow-sm`}>
              {compatibilityScore}%
            </div>
            <div className="text-sm text-gray-500 font-medium">
              {getScoreLabel(compatibilityScore)}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-5 relative">
        {otherUser?.bio && (
          <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-gradient-to-b from-pink-400 to-blue-400">
            <p className="text-gray-700 leading-relaxed italic">"{otherUser.bio}"</p>
          </div>
        )}

        {otherUser?.interests && otherUser.interests.length > 0 && (
          <div>
            <h4 className="font-semibold mb-3 text-gray-800 flex items-center gap-2">
              <span className="text-lg">🎯</span>
              兴趣爱好
            </h4>
            <div className="flex flex-wrap gap-2">
              {otherUser.interests.slice(0, 5).map((interest: string, index: number) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-gradient-to-r from-pink-100 to-blue-100 text-gray-700 border-0 hover:from-pink-200 hover:to-blue-200 transition-colors"
                >
                  {interest}
                </Badge>
              ))}
              {otherUser.interests.length > 5 && (
                <Badge variant="outline" className="text-xs border-gray-300 text-gray-600">
                  +{otherUser.interests.length - 5}
                </Badge>
              )}
            </div>
          </div>
        )}

        {match.aiAnalysis?.explanation && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">🤖</span>
              <h4 className="font-semibold text-gray-800">AI 匹配分析</h4>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="w-full text-left justify-start p-0 h-auto font-normal text-gray-600 hover:text-gray-800"
            >
              {showDetails ? '收起分析' : '展开查看详细分析'}
            </Button>
            {showDetails && (
              <div className="mt-3 p-3 bg-white/70 rounded-md text-sm text-gray-700 leading-relaxed">
                {match.aiAnalysis.explanation}
              </div>
            )}
          </div>
        )}

        <Link href={`/match/${match.id}`} className="block">
          <Button
            variant="outline"
            size="lg"
            className="w-full flex items-center gap-2 bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 border-purple-200 hover:border-purple-300 transition-all duration-300 h-12"
          >
            <Sparkles className="w-5 h-5 text-purple-600" />
            <span className="text-purple-700 font-medium">查看完整AI分析</span>
          </Button>
        </Link>

        <div className="flex gap-3 pt-2">
          <Button
            variant="outline"
            size="lg"
            onClick={() => onPass(match.id)}
            className="flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600 hover:border-red-300 hover:bg-red-50 transition-all duration-300 h-12"
          >
            <X className="w-5 h-5" />
            跳过
          </Button>
          <Button
            size="lg"
            onClick={() => onLike(match.id)}
            className="flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 shadow-lg hover:shadow-xl transition-all duration-300 h-12"
          >
            <Heart className="w-5 h-5" />
            喜欢
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
