'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, X, MessageCircle, Star } from 'lucide-react';

interface MatchCardProps {
  match: any;
  onLike: (matchId: string) => void;
  onPass: (matchId: string) => void;
  onViewConversation: (matchId: string) => void;
}

export function MatchCard({ match, onLike, onPass, onViewConversation }: MatchCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  const otherUser = match.otherUser || match.user2;
  const compatibilityScore = match.compatibilityScore || 0;

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return '高度匹配';
    if (score >= 60) return '中等匹配';
    return '低度匹配';
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl">{otherUser?.name}</CardTitle>
            <CardDescription>
              {otherUser?.age}岁 · {otherUser?.location}
            </CardDescription>
          </div>
          <div className="text-right">
            <div className={`text-2xl font-bold ${getScoreColor(compatibilityScore)}`}>
              {compatibilityScore}%
            </div>
            <div className="text-sm text-gray-500">
              {getScoreLabel(compatibilityScore)}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {otherUser?.bio && (
          <p className="text-gray-700">{otherUser.bio}</p>
        )}

        {otherUser?.interests && otherUser.interests.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">兴趣爱好</h4>
            <div className="flex flex-wrap gap-1">
              {otherUser.interests.slice(0, 5).map((interest: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {interest}
                </Badge>
              ))}
              {otherUser.interests.length > 5 && (
                <Badge variant="outline" className="text-xs">
                  +{otherUser.interests.length - 5}
                </Badge>
              )}
            </div>
          </div>
        )}

        {match.aiAnalysis?.explanation && (
          <div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="w-full"
            >
              {showDetails ? '隐藏' : '查看'} AI 分析
            </Button>
            {showDetails && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md text-sm">
                {match.aiAnalysis.explanation}
              </div>
            )}
          </div>
        )}

        {match.conversationSimulation && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewConversation(match.id)}
            className="w-full flex items-center gap-2"
          >
            <MessageCircle className="w-4 h-4" />
            查看模拟对话
          </Button>
        )}

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            size="lg"
            onClick={() => onPass(match.id)}
            className="flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600"
          >
            <X className="w-5 h-5" />
            跳过
          </Button>
          <Button
            size="lg"
            onClick={() => onLike(match.id)}
            className="flex-1 flex items-center justify-center gap-2 bg-pink-600 hover:bg-pink-700"
          >
            <Heart className="w-5 h-5" />
            喜欢
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
