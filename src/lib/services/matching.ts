import { db } from '@/lib/db';
import { users, userProfiles, matches, type User, type UserProfile, type NewMatch } from '@/lib/db/schema';
import { eq, and, or, ne } from 'drizzle-orm';
import { GeminiService } from './gemini';

export class MatchingService {
  static async findPotentialMatches(userId: string, limit: number = 10): Promise<User[]> {
    // Get current user's profile
    const currentUser = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser.length) {
      throw new Error('User not found');
    }

    // Get users who haven't been matched with current user yet
    const existingMatches = await db
      .select({ matchedUserId: matches.user2Id })
      .from(matches)
      .where(eq(matches.user1Id, userId));

    const existingMatchIds = existingMatches.map(m => m.matchedUserId);

    // Find potential matches (excluding current user and already matched users)
    const potentialMatches = await db
      .select()
      .from(users)
      .where(
        and(
          ne(users.id, userId),
          users.isActive.eq(true),
          // Add more filtering criteria here based on preferences
        )
      )
      .limit(limit);

    // Filter out already matched users
    return potentialMatches.filter(user => !existingMatchIds.includes(user.id));
  }

  static async createMatch(user1Id: string, user2Id: string): Promise<any> {
    try {
      // Get both users' profiles
      const [user1Data, user2Data] = await Promise.all([
        this.getUserWithProfile(user1Id),
        this.getUserWithProfile(user2Id),
      ]);

      if (!user1Data || !user2Data) {
        throw new Error('One or both users not found');
      }

      // Generate AI analysis
      const [personalitySummary1, personalitySummary2] = await Promise.all([
        GeminiService.generatePersonalitySummary({
          ...user1Data.user,
          ...user1Data.profile,
        }),
        GeminiService.generatePersonalitySummary({
          ...user2Data.user,
          ...user2Data.profile,
        }),
      ]);

      // Calculate compatibility score
      const compatibilityScore = await GeminiService.calculateCompatibilityScore(
        { ...user1Data.user, ...user1Data.profile, personalitySummary: personalitySummary1 },
        { ...user2Data.user, ...user2Data.profile, personalitySummary: personalitySummary2 }
      );

      // Simulate conversation
      const conversationSimulation = await GeminiService.simulateConversation(
        { ...user1Data.user, ...user1Data.profile, personalitySummary: personalitySummary1 },
        { ...user2Data.user, ...user2Data.profile, personalitySummary: personalitySummary2 }
      );

      // Generate match explanation
      const explanation = await GeminiService.generateMatchExplanation(
        { ...user1Data.user, ...user1Data.profile },
        { ...user2Data.user, ...user2Data.profile },
        compatibilityScore
      );

      // Create match record
      const matchData: NewMatch = {
        user1Id,
        user2Id,
        compatibilityScore,
        aiAnalysis: {
          user1PersonalitySummary: personalitySummary1,
          user2PersonalitySummary: personalitySummary2,
          explanation,
        },
        conversationSimulation,
        status: 'pending',
      };

      const [match] = await db.insert(matches).values(matchData).returning();

      return {
        match,
        user1: user1Data.user,
        user2: user2Data.user,
        compatibilityScore,
        explanation,
        conversationSimulation,
      };
    } catch (error) {
      console.error('Error creating match:', error);
      throw error;
    }
  }

  static async getUserMatches(userId: string): Promise<any[]> {
    const userMatches = await db
      .select()
      .from(matches)
      .where(or(eq(matches.user1Id, userId), eq(matches.user2Id, userId)));

    const enrichedMatches = await Promise.all(
      userMatches.map(async (match) => {
        const otherUserId = match.user1Id === userId ? match.user2Id : match.user1Id;
        const otherUserData = await this.getUserWithProfile(otherUserId);
        
        return {
          ...match,
          otherUser: otherUserData?.user,
          otherUserProfile: otherUserData?.profile,
        };
      })
    );

    return enrichedMatches;
  }

  static async updateMatchStatus(matchId: string, userId: string, liked: boolean): Promise<any> {
    const [match] = await db
      .select()
      .from(matches)
      .where(eq(matches.id, matchId))
      .limit(1);

    if (!match) {
      throw new Error('Match not found');
    }

    const isUser1 = match.user1Id === userId;
    const updateData: any = {};

    if (isUser1) {
      updateData.user1Liked = liked;
      updateData.user1Viewed = true;
    } else {
      updateData.user2Liked = liked;
      updateData.user2Viewed = true;
    }

    // Check if both users have liked each other
    const otherUserLiked = isUser1 ? match.user2Liked : match.user1Liked;
    if (liked && otherUserLiked) {
      updateData.status = 'mutual_like';
    } else if (!liked) {
      updateData.status = 'rejected';
    }

    const [updatedMatch] = await db
      .update(matches)
      .set(updateData)
      .where(eq(matches.id, matchId))
      .returning();

    return updatedMatch;
  }

  private static async getUserWithProfile(userId: string): Promise<{ user: User; profile: UserProfile | null } | null> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) return null;

    const [profile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId))
      .limit(1);

    return { user, profile: profile || null };
  }

  static async generateDailyMatches(userId: string): Promise<any[]> {
    try {
      const potentialMatches = await this.findPotentialMatches(userId, 5);
      const matches = [];

      for (const potentialMatch of potentialMatches) {
        try {
          const match = await this.createMatch(userId, potentialMatch.id);
          matches.push(match);
        } catch (error) {
          console.error(`Error creating match with user ${potentialMatch.id}:`, error);
          // Continue with other matches even if one fails
        }
      }

      return matches;
    } catch (error) {
      console.error('Error generating daily matches:', error);
      throw error;
    }
  }
}
