import OpenAI from 'openai';

const openrouter = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY,
});

export class GeminiService {
  private static model = 'google/gemini-2.5-flash-preview-05-20';

  static async generatePersonalitySummary(userProfile: any): Promise<any> {
    const prompt = `
    基于以下用户信息，生成一个详细的人格摘要：

    基本信息：
    - 姓名：${userProfile.name}
    - 年龄：${userProfile.age}
    - 性别：${userProfile.gender}
    - 所在地：${userProfile.location}

    个人描述：
    - 个人简介：${userProfile.bio}
    - 自我描述：${userProfile.selfDescription}
    - 兴趣爱好：${userProfile.interests?.join(', ')}
    - 寻找对象：${userProfile.lookingFor}
    - 感情目标：${userProfile.relationshipGoals}

    请分析这个人的：
    1. 性格特征（外向/内向、开放性、责任心等）
    2. 价值观和生活态度
    3. 社交偏好和沟通风格
    4. 感情需求和期望
    5. 生活方式和兴趣匹配度

    请用中文回答，格式为结构化的JSON，包含以下字段：
    {
      "personalityTraits": {
        "extraversion": 0-100,
        "openness": 0-100,
        "conscientiousness": 0-100,
        "agreeableness": 0-100,
        "neuroticism": 0-100
      },
      "values": ["价值观1", "价值观2", ...],
      "communicationStyle": "沟通风格描述",
      "relationshipNeeds": "感情需求描述",
      "lifestyle": "生活方式描述",
      "summary": "整体人格摘要"
    }
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: 'json_object' },
      });
      const content = completion.choices[0].message.content;
      if (content) {
        return JSON.parse(content);
      }
      return { error: 'Failed to parse response' };
    } catch (error: any) {
      console.error('Error generating personality summary:', error.stack);
      throw error;
    }
  }

  static async simulateConversation(user1Profile: any, user2Profile: any): Promise<any> {
    const prompt = `
    模拟两个用户之间的对话，分析他们的兼容性：

    用户1 - ${user1Profile.name}：
    ${JSON.stringify(user1Profile, null, 2)}

    用户2 - ${user2Profile.name}：
    ${JSON.stringify(user2Profile, null, 2)}

    请模拟他们第一次见面时的对话（5-8轮对话），并分析：
    1. 对话流畅度
    2. 共同话题
    3. 价值观契合度
    4. 沟通风格匹配
    5. 潜在冲突点

    重要：在对话中使用真实的用户名称（${user1Profile.name} 和 ${user2Profile.name}），而不是 "user1" 或 "user2"。

    返回JSON格式：
    {
      "conversation": [
        {"speaker": "${user1Profile.name}", "message": "对话内容"},
        {"speaker": "${user2Profile.name}", "message": "对话内容"},
        ...
      ],
      "analysis": {
        "conversationFlow": 0-100,
        "commonTopics": ["话题1", "话题2", ...],
        "valueAlignment": 0-100,
        "communicationMatch": 0-100,
        "potentialConflicts": ["冲突点1", "冲突点2", ...],
        "overallCompatibility": 0-100
      }
    }
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: 'json_object' },
      });
      const content = completion.choices[0].message.content;
      if (content) {
        return JSON.parse(content);
      }
      return { error: 'Failed to parse response' };
    } catch (error) {
      console.error('Error simulating conversation:', error);
      throw error;
    }
  }

  static async calculateCompatibilityScore(user1Profile: any, user2Profile: any): Promise<number> {
    const prompt = `
    基于以下两个用户的资料，计算他们的兼容性分数（0-100）：

    用户1：
    ${JSON.stringify(user1Profile, null, 2)}

    用户2：
    ${JSON.stringify(user2Profile, null, 2)}

    考虑因素：
    1. 性格互补性（30%）
    2. 价值观一致性（25%）
    3. 兴趣爱好重叠（20%）
    4. 生活方式匹配（15%）
    5. 感情目标一致（10%）

    只返回一个0-100之间的数字。
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
      });
      const text = completion.choices[0].message.content || '';
      const score = parseInt(text.match(/\d+/)?.[0] || '0');
      return Math.min(Math.max(score, 0), 100);
    } catch (error) {
      console.error('Error calculating compatibility score:', error);
      return 0;
    }
  }

  static async generateMatchExplanation(user1Profile: any, user2Profile: any, score: number): Promise<string> {
    const prompt = `
    解释为什么这两个用户的兼容性分数是${score}分：

    用户1：${user1Profile.name}
    用户2：${user2Profile.name}

    请提供：
    1. 匹配的优势（为什么他们适合）
    2. 潜在的挑战（需要注意的地方）
    3. 建议的相处方式
    4. 发展前景预测

    用温暖、专业的语调，给出建设性的建议。
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
      });
      return completion.choices[0].message.content || '暂时无法生成匹配解释，请稍后重试。';
    } catch (error) {
      console.error('Error generating match explanation:', error);
      return '暂时无法生成匹配解释，请稍后重试。';
    }
  }

  static async generateComprehensiveAnalysis(user1Profile: any, user2Profile: any, score: number): Promise<any> {
    const prompt = `
    基于以下两个用户的资料，生成详细的匹配分析：

    用户1：
    ${JSON.stringify(user1Profile, null, 2)}

    用户2：
    ${JSON.stringify(user2Profile, null, 2)}

    兼容性分数：${score}分

    请生成结构化的分析报告，包含：
    1. 详细的匹配解释
    2. 3-5个匹配优势
    3. 2-3个需要注意的挑战
    4. 3-5个建议的聊天话题

    返回JSON格式：
    {
      "explanation": "详细的匹配解释文本",
      "strengths": ["优势1", "优势2", "优势3"],
      "challenges": ["挑战1", "挑战2"],
      "suggestions": ["话题1", "话题2", "话题3"]
    }
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: 'json_object' },
      });

      const content = completion.choices[0].message.content;
      if (content) {
        return JSON.parse(content);
      }

      // Fallback: create a structured response
      return {
        explanation: '基于双方的资料分析，你们在多个方面都有很好的契合度。',
        strengths: ['价值观契合', '兴趣相投', '性格互补'],
        challenges: ['需要更多了解', '沟通方式磨合'],
        suggestions: ['分享兴趣爱好', '聊聊人生目标', '交流价值观']
      };
    } catch (error) {
      console.error('Error generating comprehensive analysis:', error);
      // Return fallback data
      return {
        explanation: '暂时无法生成详细分析，请稍后重试。',
        strengths: ['等待AI分析'],
        challenges: ['等待AI分析'],
        suggestions: ['等待AI分析']
      };
    }
  }

  static async generateDatePlan(user1Profile: any, user2Profile: any): Promise<any> {
    const prompt = `
    基于以下两个用户的资料，为他们设计一个完美的约会计划：

    用户1：
    ${JSON.stringify(user1Profile, null, 2)}

    用户2：
    ${JSON.stringify(user2Profile, null, 2)}

    请设计一个考虑双方兴趣爱好、性格特点和偏好的约会计划。包含：
    1. 约会主题和理念
    2. 具体的约会活动安排（时间线）
    3. 推荐的地点类型
    4. 注意事项和建议
    5. 备选方案

    返回JSON格式：
    {
      "theme": "约会主题",
      "concept": "约会理念描述",
      "timeline": [
        {
          "time": "时间段",
          "activity": "活动内容",
          "location": "地点类型",
          "reason": "选择理由"
        }
      ],
      "recommendations": {
        "locations": ["推荐地点1", "推荐地点2", ...],
        "tips": ["建议1", "建议2", ...],
        "alternatives": ["备选方案1", "备选方案2", ...]
      },
      "budget": "预算建议",
      "duration": "约会时长"
    }
    `;

    try {
      const completion = await openrouter.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: prompt }],
        response_format: { type: 'json_object' },
      });

      const content = completion.choices[0].message.content;
      if (content) {
        return JSON.parse(content);
      }

      // Fallback: create a basic date plan
      return {
        theme: "轻松愉快的初次约会",
        concept: "选择轻松的环境，让双方都能感到舒适，有充分的交流机会。",
        timeline: [
          {
            time: "下午2:00-3:30",
            activity: "咖啡厅聊天",
            location: "安静的咖啡厅",
            reason: "轻松的环境有利于深入交流"
          },
          {
            time: "下午3:30-5:00",
            activity: "公园散步",
            location: "附近的公园",
            reason: "自然环境能缓解紧张情绪"
          }
        ],
        recommendations: {
          locations: ["星巴克", "当地特色咖啡厅", "公园", "美术馆"],
          tips: ["保持轻松的心态", "准备一些有趣的话题", "注意倾听"],
          alternatives: ["如果天气不好可以选择室内活动", "可以根据聊天情况延长或缩短时间"]
        },
        budget: "100-200元",
        duration: "2-3小时"
      };
    } catch (error) {
      console.error('Error generating date plan:', error);
      // Return fallback data
      return {
        theme: "经典约会",
        concept: "简单而美好的相遇。",
        timeline: [
          {
            time: "下午",
            activity: "咖啡约会",
            location: "咖啡厅",
            reason: "轻松愉快的环境"
          }
        ],
        recommendations: {
          locations: ["咖啡厅"],
          tips: ["保持自然"],
          alternatives: ["灵活调整"]
        },
        budget: "适中",
        duration: "2小时"
      };
    }
  }
}
