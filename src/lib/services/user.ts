import { db } from '@/lib/db';
import { users, userProfiles, type User, type UserProfile, type NewUser, type NewUserProfile } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export class UserService {
  static async createUser(userData: NewUser): Promise<User> {
    const [user] = await db.insert(users).values(userData).returning();
    return user;
  }

  static async getUserById(id: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || null;
  }

  static async getUserByEmail(email: string): Promise<User | null> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || null;
  }

  static async updateUser(id: string, userData: Partial<User>): Promise<User> {
    const [user] = await db
      .update(users)
      .set({ ...userData, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  static async createUserProfile(profileData: NewUserProfile): Promise<UserProfile> {
    const [profile] = await db.insert(userProfiles).values(profileData).returning();
    return profile;
  }

  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    const [profile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, userId));
    return profile || null;
  }

  static async updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    const [profile] = await db
      .update(userProfiles)
      .set({ ...profileData, updatedAt: new Date() })
      .where(eq(userProfiles.userId, userId))
      .returning();
    return profile;
  }

  static async getUserWithProfile(userId: string): Promise<{ user: User; profile: UserProfile | null } | null> {
    const user = await this.getUserById(userId);
    if (!user) return null;

    const profile = await this.getUserProfile(userId);
    return { user, profile };
  }
}
