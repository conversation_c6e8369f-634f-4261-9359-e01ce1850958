import { db } from '@/lib/db';
import { users, type NewUser } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import type { User as SupabaseUser } from '@supabase/supabase-js';

export class AuthSyncService {
  /**
   * 同步 Supabase Auth 用户到我们的 users 表
   * 这个函数会在用户首次登录或邮箱验证后调用
   */
  static async syncUserToDatabase(supabaseUser: SupabaseUser): Promise<any> {
    try {
      // 检查用户是否已经存在于我们的数据库中
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.id, supabaseUser.id))
        .limit(1);

      if (existingUser.length > 0) {
        // 用户已存在，更新最后登录时间
        const [updatedUser] = await db
          .update(users)
          .set({ 
            updatedAt: new Date(),
            email: supabaseUser.email || existingUser[0].email,
          })
          .where(eq(users.id, supabaseUser.id))
          .returning();
        
        return updatedUser;
      } else {
        // 用户不存在，创建新用户记录
        const newUserData: NewUser = {
          id: supabaseUser.id,
          email: supabaseUser.email!,
          name: supabaseUser.user_metadata?.name || null,
          avatar: supabaseUser.user_metadata?.avatar_url || null,
          isActive: true,
        };

        const [newUser] = await db
          .insert(users)
          .values(newUserData)
          .returning();

        return newUser;
      }
    } catch (error) {
      console.error('Error syncing user to database:', error);
      throw error;
    }
  }

  /**
   * 获取完整的用户信息（包括 profile）
   */
  static async getCompleteUserInfo(userId: string) {
    try {
      const user = await db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user.length) {
        return null;
      }

      return user[0];
    } catch (error) {
      console.error('Error getting complete user info:', error);
      return null;
    }
  }

  /**
   * 检查用户是否需要完善资料
   */
  static async checkProfileCompleteness(userId: string): Promise<boolean> {
    try {
      const user = await this.getCompleteUserInfo(userId);
      
      if (!user) return false;

      // 检查基本信息是否完整
      const hasBasicInfo = user.name && user.age && user.gender && user.location;
      
      return !!hasBasicInfo;
    } catch (error) {
      console.error('Error checking profile completeness:', error);
      return false;
    }
  }
}
