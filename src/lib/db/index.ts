import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Create the connection
const connectionString = process.env.DATABASE_URL;

let db: ReturnType<typeof drizzle>;

if (connectionString) {
  // Create postgres client
  const client = postgres(connectionString, {
    max: 1,
    idle_timeout: 20,
    connect_timeout: 10,
  });

  // Create drizzle instance
  db = drizzle(client, { schema });
} else {
  // Mock database for build time - create a minimal mock
  const mockClient = {} as any;
  db = drizzle(mockClient, { schema });
}

export { db };

// Export schema for use in other files
export * from './schema';
