'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Heart, X, Star, ThumbsUp, ThumbsDown, Play, Pause } from 'lucide-react';
import Link from 'next/link';

interface Message {
  speaker: 'user1' | 'user2';
  message: string;
}

export default function MatchDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [match, setMatch] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showAllMessages, setShowAllMessages] = useState(false);
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 模拟数据 - 在实际应用中这里会从API获取
  const mockMatch = {
    id: '1',
    compatibilityScore: 85,
    otherUser: {
      name: '小雨',
      age: 26,
      location: '北京',
      bio: '喜欢阅读和旅行，寻找有趣的灵魂',
      interests: ['阅读', '旅行', '摄影', '咖啡', '电影', '音乐', '瑜伽'],
      avatar: '🌸'
    },
    aiAnalysis: {
      explanation: '你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补，你们可能会在艺术、文化和人生哲学方面有很多共同话题。建议从共同的兴趣话题开始交流，比如最近读过的书或者想去的旅行目的地。',
      strengths: ['价值观高度一致', '兴趣爱好互补', '都热爱学习成长', '沟通风格匹配'],
      challenges: ['生活节奏可能不同', '需要平衡独处与社交时间'],
      suggestions: ['从共同兴趣开始聊天', '分享彼此的读书心得', '计划一次文化之旅']
    },
    conversationSimulation: {
      conversation: [
        { speaker: 'user1', message: '你好！看到你也喜欢旅行，最近去过哪里？' },
        { speaker: 'user2', message: '你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？' },
        { speaker: 'user1', message: '云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。' },
        { speaker: 'user2', message: '哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。' },
        { speaker: 'user1', message: '是的！我特别想去布达拉宫，还想尝试一下藏式瑜伽，听说在高原上练瑜伽是完全不同的体验。' },
        { speaker: 'user2', message: '这个想法太棒了！我也练瑜伽，在大自然中练习确实会有不一样的感受。我们可以一起规划路线吗？' },
        { speaker: 'user1', message: '当然可以！我已经收集了一些攻略，我们可以分享一下彼此的想法。' },
        { speaker: 'user2', message: '太好了！我觉得我们会有很多共同话题，期待更深入的交流。' }
      ],
      analysis: {
        conversationFlow: 92,
        valueAlignment: 88,
        communicationMatch: 90,
        overallCompatibility: 85,
        commonTopics: ['旅行', '瑜伽', '文化探索', '自然风光'],
        potentialConflicts: ['时间安排差异', '旅行预算考虑']
      }
    },
    status: 'pending'
  };

  useEffect(() => {
    // 模拟加载数据
    setTimeout(() => {
      setMatch(mockMatch);
      setLoading(false);
    }, 500);
  }, []);

  const startConversationAnimation = () => {
    if (!match?.conversationSimulation?.conversation) return;
    
    setIsPlaying(true);
    setCurrentMessageIndex(0);
    setShowAllMessages(false);
    
    intervalRef.current = setInterval(() => {
      setCurrentMessageIndex(prev => {
        const nextIndex = prev + 1;
        if (nextIndex >= match.conversationSimulation.conversation.length) {
          setIsPlaying(false);
          setShowAllMessages(true);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          return prev;
        }
        return nextIndex;
      });
    }, 2000); // 每2秒显示一条消息
  };

  const pauseConversationAnimation = () => {
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const showAllConversation = () => {
    setIsPlaying(false);
    setShowAllMessages(true);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    setCurrentMessageIndex(match?.conversationSimulation?.conversation?.length || 0);
  };

  const handleFeedback = async (type: 'accurate' | 'inaccurate', rating?: number) => {
    // 这里会调用API保存反馈
    console.log('Feedback:', { type, rating: rating || selectedRating });
    setFeedbackSubmitted(true);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return '高度匹配';
    if (score >= 60) return '中等匹配';
    return '低度匹配';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>加载匹配详情...</p>
        </div>
      </div>
    );
  }

  if (!match) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">未找到匹配信息</p>
          <Link href="/dashboard">
            <Button>返回首页</Button>
          </Link>
        </div>
      </div>
    );
  }

  const conversation = match.conversationSimulation?.conversation || [];
  const analysis = match.conversationSimulation?.analysis || {};
  const visibleMessages = showAllMessages ? conversation : conversation.slice(0, currentMessageIndex + 1);

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/dashboard" className="flex items-center gap-2 text-gray-600 hover:text-gray-900">
              <ArrowLeft className="w-5 h-5" />
              返回
            </Link>
            <h1 className="text-lg font-semibold">匹配详情</h1>
            <div className="w-16"></div> {/* 占位符保持居中 */}
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* 用户信息卡片 */}
        <Card className="overflow-hidden">
          <div className="bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl">
                  {match.otherUser.avatar}
                </div>
                <div>
                  <h2 className="text-2xl font-bold">{match.otherUser.name}</h2>
                  <p className="text-pink-100">{match.otherUser.age}岁 · {match.otherUser.location}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">{match.compatibilityScore}%</div>
                <div className="text-pink-100">{getScoreLabel(match.compatibilityScore)}</div>
              </div>
            </div>
          </div>
          
          <CardContent className="p-6">
            <p className="text-gray-700 mb-4">{match.otherUser.bio}</p>
            
            <div className="mb-6">
              <h4 className="font-medium mb-3">兴趣爱好</h4>
              <div className="flex flex-wrap gap-2">
                {match.otherUser.interests.map((interest: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-sm">
                    {interest}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                size="lg"
                className="flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600"
              >
                <X className="w-5 h-5" />
                跳过
              </Button>
              <Button
                size="lg"
                className="flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700"
              >
                <Heart className="w-5 h-5" />
                喜欢
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* AI分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span className="text-2xl">🤖</span>
              AI 深度分析
            </CardTitle>
            <CardDescription>
              基于双方资料的智能匹配分析
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="font-medium mb-2">匹配解析</h4>
              <p className="text-gray-700 leading-relaxed">{match.aiAnalysis.explanation}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3 text-green-600">匹配优势</h4>
                <ul className="space-y-2">
                  {match.aiAnalysis.strengths.map((strength: string, index: number) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-3 text-yellow-600">注意事项</h4>
                <ul className="space-y-2">
                  {match.aiAnalysis.challenges.map((challenge: string, index: number) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                      {challenge}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 text-blue-600">建议话题</h4>
              <div className="flex flex-wrap gap-2">
                {match.aiAnalysis.suggestions.map((suggestion: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-blue-600 border-blue-200">
                    {suggestion}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 对话模拟 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-2xl">💬</span>
                  AI 对话模拟
                </CardTitle>
                <CardDescription>
                  预测你们第一次聊天的场景
                </CardDescription>
              </div>
              
              <div className="flex gap-2">
                {!isPlaying && currentMessageIndex === 0 && (
                  <Button onClick={startConversationAnimation} variant="outline" size="sm">
                    <Play className="w-4 h-4 mr-2" />
                    播放对话
                  </Button>
                )}
                
                {isPlaying && (
                  <Button onClick={pauseConversationAnimation} variant="outline" size="sm">
                    <Pause className="w-4 h-4 mr-2" />
                    暂停
                  </Button>
                )}
                
                {currentMessageIndex > 0 && !showAllMessages && (
                  <Button onClick={showAllConversation} variant="outline" size="sm">
                    显示全部
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto">
              {visibleMessages.map((message: Message, index: number) => (
                <div
                  key={index}
                  className={`flex ${message.speaker === 'user1' ? 'justify-end' : 'justify-start'} ${
                    index === currentMessageIndex && isPlaying ? 'animate-pulse' : ''
                  }`}
                >
                  <div
                    className={`max-w-[70%] p-3 rounded-lg transition-all duration-500 ${
                      message.speaker === 'user1'
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                        : 'bg-white border shadow-sm'
                    } ${index <= currentMessageIndex ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'}`}
                  >
                    <div className="text-xs opacity-70 mb-1">
                      {message.speaker === 'user1' ? '你' : match.otherUser.name}
                    </div>
                    <div className="text-sm">{message.message}</div>
                  </div>
                </div>
              ))}
              
              {isPlaying && currentMessageIndex < conversation.length && (
                <div className="flex justify-center">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}
            </div>

            {/* 分析结果 */}
            {showAllMessages && (
              <div className="mt-6 space-y-4">
                <h4 className="font-medium">对话分析</h4>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">对话流畅度</span>
                      <span className={`text-sm font-medium ${getScoreColor(analysis.conversationFlow || 0)}`}>
                        {analysis.conversationFlow || 0}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">价值观契合</span>
                      <span className={`text-sm font-medium ${getScoreColor(analysis.valueAlignment || 0)}`}>
                        {analysis.valueAlignment || 0}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">沟通匹配度</span>
                      <span className={`text-sm font-medium ${getScoreColor(analysis.communicationMatch || 0)}`}>
                        {analysis.communicationMatch || 0}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">整体兼容性</span>
                      <span className={`text-sm font-medium ${getScoreColor(analysis.overallCompatibility || 0)}`}>
                        {analysis.overallCompatibility || 0}%
                      </span>
                    </div>
                  </div>
                </div>

                {analysis.commonTopics && analysis.commonTopics.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium mb-2">共同话题</h5>
                    <div className="flex flex-wrap gap-1">
                      {analysis.commonTopics.map((topic: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {topic}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 反馈区域 */}
            {showAllMessages && (
              <div className="mt-6 pt-6 border-t">
                <h4 className="font-medium mb-3">这个对话模拟准确吗？</h4>
                
                {!feedbackSubmitted ? (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <span className="text-sm">准确度评分：</span>
                      <div className="flex gap-1">
                        {[1, 2, 3, 4, 5].map((rating) => (
                          <button
                            key={rating}
                            onClick={() => setSelectedRating(rating)}
                            className={`p-1 transition-colors ${
                              selectedRating && selectedRating >= rating
                                ? 'text-yellow-500'
                                : 'text-gray-300 hover:text-yellow-400'
                            }`}
                          >
                            <Star className="w-4 h-4 fill-current" />
                          </button>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleFeedback('accurate')}
                        className="flex items-center gap-2"
                      >
                        <ThumbsUp className="w-4 h-4" />
                        很准确
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleFeedback('inaccurate')}
                        className="flex items-center gap-2"
                      >
                        <ThumbsDown className="w-4 h-4" />
                        不太准确
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-green-600 text-sm bg-green-50 p-3 rounded-md">
                    ✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
