import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { AuthSyncService } from '@/lib/services/auth-sync';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    try {
      // 交换 code 获取 session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Auth callback error:', error);
        return NextResponse.redirect(requestUrl.origin + '/auth/login?error=callback_error');
      }

      // 如果有用户信息，同步到我们的数据库
      if (data.user) {
        await AuthSyncService.syncUserToDatabase(data.user);

        // 检查用户资料是否完整
        const isProfileComplete = await AuthSyncService.checkProfileCompleteness(data.user.id);

        if (!isProfileComplete) {
          // 如果资料不完整，重定向到资料完善页面
          return NextResponse.redirect(requestUrl.origin + '/profile?welcome=true');
        }
      }
    } catch (error) {
      console.error('Error in auth callback:', error);
      return NextResponse.redirect(requestUrl.origin + '/auth/login?error=sync_error');
    }
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(requestUrl.origin + '/dashboard');
}
