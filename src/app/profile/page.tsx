'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';

interface UserProfile {
  name: string;
  age: number;
  gender: string;
  location: string;
  bio: string;
  interests: string[];
  selfDescription: string;
  lookingFor: string;
  relationshipGoals: string;
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile>({
    name: '',
    age: 0,
    gender: '',
    location: '',
    bio: '',
    interests: [],
    selfDescription: '',
    lookingFor: '',
    relationshipGoals: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [interestInput, setInterestInput] = useState('');
  const [isWelcome, setIsWelcome] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // 检查是否是首次访问
    const urlParams = new URLSearchParams(window.location.search);
    setIsWelcome(urlParams.get('welcome') === 'true');

    checkUserAndLoadProfile();
  }, []);

  const checkUserAndLoadProfile = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // 加载用户资料
    try {
      const response = await fetch('/api/profile');
      if (response.ok) {
        const { user: userData, profile: profileData } = await response.json();

        if (userData) {
          setProfile(prev => ({
            ...prev,
            name: userData.name || '',
            age: userData.age || 0,
            gender: userData.gender || '',
            location: userData.location || '',
            bio: userData.bio || '',
            interests: userData.interests || [],
          }));
        }

        if (profileData) {
          setProfile(prev => ({
            ...prev,
            selfDescription: profileData.selfDescription || '',
            lookingFor: profileData.lookingFor || '',
            relationshipGoals: profileData.relationshipGoals || '',
          }));
        }
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const addInterest = () => {
    if (interestInput.trim() && !profile.interests.includes(interestInput.trim())) {
      setProfile(prev => ({
        ...prev,
        interests: [...prev.interests, interestInput.trim()]
      }));
      setInterestInput('');
    }
  };

  const removeInterest = (interest: string) => {
    setProfile(prev => ({
      ...prev,
      interests: prev.interests.filter(i => i !== interest)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profile),
      });

      if (response.ok) {
        setSuccess('资料保存成功！');

        // 如果是首次完善资料，跳转到 dashboard
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('welcome') === 'true') {
          setTimeout(() => {
            router.push('/dashboard');
          }, 1500);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || '保存失败，请重试');
      }
    } catch (err) {
      setError('保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <Card>
          <CardHeader>
            <CardTitle>
              {isWelcome ? '欢迎加入灵犀AI！' : '个人资料'}
            </CardTitle>
            <CardDescription>
              {isWelcome
                ? '请完善您的个人信息，让AI为您找到最合适的伴侣'
                : '完善您的个人信息，让AI更好地为您匹配'
              }
            </CardDescription>
            {isWelcome && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  🎉 注册成功！完善资料后即可开始您的智能匹配之旅。
                </p>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="text-red-500 text-sm">{error}</div>
              )}
              {success && (
                <div className="text-green-500 text-sm">{success}</div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">姓名</Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => setProfile(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="age">年龄</Label>
                  <Input
                    id="age"
                    type="number"
                    value={profile.age || ''}
                    onChange={(e) => setProfile(prev => ({ ...prev, age: parseInt(e.target.value) || 0 }))}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="gender">性别</Label>
                  <select
                    id="gender"
                    value={profile.gender}
                    onChange={(e) => setProfile(prev => ({ ...prev, gender: e.target.value }))}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  >
                    <option value="">请选择</option>
                    <option value="male">男</option>
                    <option value="female">女</option>
                    <option value="other">其他</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="location">所在地</Label>
                  <Input
                    id="location"
                    value={profile.location}
                    onChange={(e) => setProfile(prev => ({ ...prev, location: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio">个人简介</Label>
                <Textarea
                  id="bio"
                  value={profile.bio}
                  onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                  placeholder="简单介绍一下自己..."
                  rows={3}
                />
              </div>

              <div>
                <Label>兴趣爱好</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={interestInput}
                    onChange={(e) => setInterestInput(e.target.value)}
                    placeholder="添加兴趣爱好"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInterest())}
                  />
                  <Button type="button" onClick={addInterest}>添加</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {profile.interests.map((interest, index) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm flex items-center gap-1"
                    >
                      {interest}
                      <button
                        type="button"
                        onClick={() => removeInterest(interest)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <Label htmlFor="selfDescription">自我描述</Label>
                <Textarea
                  id="selfDescription"
                  value={profile.selfDescription}
                  onChange={(e) => setProfile(prev => ({ ...prev, selfDescription: e.target.value }))}
                  placeholder="详细描述一下自己的性格、价值观等..."
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="lookingFor">寻找对象</Label>
                <Textarea
                  id="lookingFor"
                  value={profile.lookingFor}
                  onChange={(e) => setProfile(prev => ({ ...prev, lookingFor: e.target.value }))}
                  placeholder="描述您理想的伴侣..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="relationshipGoals">感情目标</Label>
                <Textarea
                  id="relationshipGoals"
                  value={profile.relationshipGoals}
                  onChange={(e) => setProfile(prev => ({ ...prev, relationshipGoals: e.target.value }))}
                  placeholder="您希望建立什么样的关系？"
                  rows={2}
                />
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? '保存中...' : '保存资料'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
