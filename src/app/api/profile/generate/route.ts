import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { GeminiService } from '@/lib/services/gemini';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name, age, gender, location, interests } = await request.json();

    // 验证必要字段
    if (!name || !age || !gender) {
      return NextResponse.json({ 
        error: '请先填写姓名、年龄和性别信息' 
      }, { status: 400 });
    }

    // 生成AI个人资料
    const generatedProfile = await generateProfileWithAI({
      name,
      age,
      gender,
      location,
      interests
    });

    return NextResponse.json(generatedProfile);
  } catch (error) {
    console.error('Error generating profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function generateProfileWithAI(userInfo: any) {
  const prompt = `
  基于以下用户信息，生成一个真实、吸引人的个人资料：

  用户信息：
  - 姓名：${userInfo.name}
  - 年龄：${userInfo.age}
  - 性别：${userInfo.gender}
  - 位置：${userInfo.location || '未提供'}
  - 兴趣爱好：${userInfo.interests?.join(', ') || '未提供'}

  请生成以下四个字段的内容，要求：
  1. 内容要真实可信，不要过于夸张
  2. 符合中国文化背景和表达习惯
  3. 根据年龄和性别调整语言风格
  4. 每个字段控制在合适的长度

  返回JSON格式：
  {
    "bio": "个人简介（50-100字，简洁介绍自己）",
    "selfDescription": "自我描述（100-200字，详细描述性格、价值观、生活方式等）",
    "lookingFor": "寻找对象（80-150字，描述理想伴侣的特质和期望）",
    "relationshipGoals": "感情目标（50-100字，说明希望建立什么样的关系）"
  }

  示例风格参考：
  - 如果是年轻人（20-30岁）：语言活泼一些，提到学习、工作、兴趣爱好
  - 如果是中年人（30-40岁）：语言成熟稳重，提到事业、家庭、人生规划
  - 男性：可以提到责任感、上进心、兴趣爱好
  - 女性：可以提到温柔、独立、生活品质

  请确保内容积极正面，避免负面表达。
  `;

  try {
    const result = await GeminiService.generateText(prompt);
    
    // 尝试解析JSON
    const cleanedResult = result.replace(/```json\n?|\n?```/g, '').trim();
    const parsedResult = JSON.parse(cleanedResult);
    
    // 验证返回的字段
    if (!parsedResult.bio || !parsedResult.selfDescription || 
        !parsedResult.lookingFor || !parsedResult.relationshipGoals) {
      throw new Error('AI生成的内容格式不完整');
    }
    
    return parsedResult;
  } catch (error) {
    console.error('Error parsing AI response:', error);
    
    // 如果AI生成失败，返回默认示例
    return getDefaultProfileExample(userInfo);
  }
}

function getDefaultProfileExample(userInfo: any) {
  const { name, age, gender } = userInfo;
  
  // 根据性别和年龄生成不同的默认示例
  if (gender === 'male') {
    return {
      bio: `我是${name}，${age}岁，一个积极向上的人。喜欢探索新事物，享受生活中的美好时刻。`,
      selfDescription: `我是一个比较随和的人，喜欢和朋友聊天，也享受独处的时光。工作上比较认真负责，生活中喜欢尝试新的体验。我相信真诚和善良是最重要的品质，希望能遇到志同道合的人一起分享生活的点点滴滴。`,
      lookingFor: `希望遇到一个善良、有趣的女生，我们可以一起聊天、一起探索这个世界。不需要完全相同的兴趣爱好，但希望我们能互相理解和支持。`,
      relationshipGoals: `希望能建立一段真诚、稳定的关系，从朋友开始，慢慢了解彼此，看看是否适合走得更远。`
    };
  } else {
    return {
      bio: `我是${name}，${age}岁，一个热爱生活的女生。喜欢美好的事物，相信生活中处处都有小确幸。`,
      selfDescription: `我是一个比较温和的人，喜欢和朋友分享生活中的趣事，也很享受安静的独处时光。我觉得保持好奇心很重要，总是愿意尝试新的事物。我重视真诚的交流，希望能遇到一个能够理解我、支持我的人。`,
      lookingFor: `希望遇到一个成熟、有责任心的男生，我们可以一起成长，一起面对生活中的挑战。希望他有自己的兴趣爱好，也能尊重我的选择。`,
      relationshipGoals: `希望能找到一个可以长期相伴的人，我们可以从了解开始，慢慢建立深厚的感情基础。`
    };
  }
}
