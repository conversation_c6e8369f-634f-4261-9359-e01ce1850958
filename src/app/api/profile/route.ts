import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { UserService } from '@/lib/services/user';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userWithProfile = await UserService.getUserWithProfile(user.id);
    
    return NextResponse.json({ 
      user: userWithProfile?.user,
      profile: userWithProfile?.profile 
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const profileData = await request.json();
    
    // 更新 users 表的基本信息
    const updatedUser = await UserService.updateUser(user.id, {
      name: profileData.name,
      age: profileData.age,
      gender: profileData.gender,
      location: profileData.location,
      bio: profileData.bio,
      interests: profileData.interests,
    });

    // 检查是否已有 profile 记录
    let profile = await UserService.getUserProfile(user.id);
    
    if (profile) {
      // 更新现有 profile
      profile = await UserService.updateUserProfile(user.id, {
        selfDescription: profileData.selfDescription,
        lookingFor: profileData.lookingFor,
        relationshipGoals: profileData.relationshipGoals,
      });
    } else {
      // 创建新 profile
      profile = await UserService.createUserProfile({
        userId: user.id,
        selfDescription: profileData.selfDescription,
        lookingFor: profileData.lookingFor,
        relationshipGoals: profileData.relationshipGoals,
      });
    }
    
    return NextResponse.json({ 
      success: true,
      user: updatedUser,
      profile 
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
