import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { MatchingService } from '@/lib/services/matching';
import { db } from '@/lib/db';
import { matches, users, userProfiles } from '@/lib/db/schema';
import { eq, or, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const matchId = params.id;

    // 获取匹配记录
    const [match] = await db
      .select()
      .from(matches)
      .where(
        and(
          eq(matches.id, matchId),
          or(
            eq(matches.user1Id, user.id),
            eq(matches.user2Id, user.id)
          )
        )
      )
      .limit(1);

    if (!match) {
      return NextResponse.json({ error: 'Match not found' }, { status: 404 });
    }

    // 获取对方用户信息
    const otherUserId = match.user1Id === user.id ? match.user2Id : match.user1Id;

    const [otherUser] = await db
      .select()
      .from(users)
      .where(eq(users.id, otherUserId))
      .limit(1);

    const [otherUserProfile] = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, otherUserId))
      .limit(1);

    const enrichedMatch = {
      ...match,
      otherUser,
      otherUserProfile,
    };

    return NextResponse.json({ match: enrichedMatch });
  } catch (error) {
    console.error('Error fetching match:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { liked } = await request.json();
    const params = await context.params;
    const matchId = params.id;

    const updatedMatch = await MatchingService.updateMatchStatus(
      matchId,
      user.id,
      liked
    );

    return NextResponse.json({ match: updatedMatch });
  } catch (error) {
    console.error('Error updating match status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
