import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { MatchingService } from '@/lib/services/matching';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const generateDaily = url.searchParams.get('generate_daily') === 'true';

    if (generateDaily) {
      // 生成每日匹配
      try {
        const dailyMatches = await MatchingService.generateDailyMatches(user.id);
        return NextResponse.json({ matches: dailyMatches });
      } catch (error) {
        console.error('Error generating daily matches:', error);
        // 如果生成失败，返回现有匹配
        const matches = await MatchingService.getUserMatches(user.id);
        return NextResponse.json({ matches });
      }
    } else {
      // 获取现有匹配
      const matches = await MatchingService.getUserMatches(user.id);
      return NextResponse.json({ matches });
    }
  } catch (error) {
    console.error('Error fetching matches:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { action, targetUserId } = await request.json();
    
    if (action === 'generate_daily') {
      const dailyMatches = await MatchingService.generateDailyMatches(user.id);
      return NextResponse.json({ matches: dailyMatches });
    }
    
    if (action === 'create_match' && targetUserId) {
      const match = await MatchingService.createMatch(user.id, targetUserId);
      return NextResponse.json({ match });
    }
    
    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error processing match request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
