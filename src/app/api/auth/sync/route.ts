import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { AuthSyncService } from '@/lib/services/auth-sync';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 同步用户到数据库
    await AuthSyncService.syncUserToDatabase(user);
    
    // 检查资料完整性
    const isProfileComplete = await AuthSyncService.checkProfileCompleteness(user.id);
    
    return NextResponse.json({ 
      success: true, 
      isProfileComplete,
      userId: user.id 
    });
  } catch (error) {
    console.error('Error in auth sync API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
