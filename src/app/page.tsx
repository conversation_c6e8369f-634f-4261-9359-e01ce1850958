import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Heart, Brain, MessageCircle, Shield, Star, Users } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-2">
              <Heart className="w-8 h-8 text-pink-600" />
              <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
                灵犀AI
              </h1>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/auth/login">
                <Button variant="outline">登录</Button>
              </Link>
              <Link href="/auth/register">
                <Button>注册</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            用AI找到你的
            <span className="bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
              灵魂伴侣
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            基于先进的AI技术，深度分析性格特征和价值观，为您精准匹配最合适的伴侣。
            不再是简单的外表匹配，而是真正的心灵契合。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700">
                开始寻找真爱
              </Button>
            </Link>
            <Button size="lg" variant="outline">
              了解更多
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择灵犀AI？
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              我们不只是另一个交友应用，而是您找到真爱的智能助手
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Brain className="w-12 h-12 text-blue-600 mb-4" />
                <CardTitle>AI深度分析</CardTitle>
                <CardDescription>
                  基于Gemini AI技术，深度分析用户的性格特征、价值观和生活方式，
                  生成详细的人格画像
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <MessageCircle className="w-12 h-12 text-green-600 mb-4" />
                <CardTitle>对话模拟</CardTitle>
                <CardDescription>
                  AI模拟你们的第一次对话，预测沟通风格和话题契合度，
                  让你提前了解彼此的交流方式
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Heart className="w-12 h-12 text-pink-600 mb-4" />
                <CardTitle>精准匹配</CardTitle>
                <CardDescription>
                  综合考虑性格互补、价值观一致、兴趣重叠等多个维度，
                  计算出科学的兼容性分数
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Shield className="w-12 h-12 text-purple-600 mb-4" />
                <CardTitle>隐私保护</CardTitle>
                <CardDescription>
                  双向确认机制，只有互相喜欢才能开始聊天，
                  保护用户隐私和安全
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Star className="w-12 h-12 text-yellow-600 mb-4" />
                <CardTitle>持续学习</CardTitle>
                <CardDescription>
                  AI会根据用户反馈不断学习和优化，
                  提供越来越准确的匹配建议
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <Users className="w-12 h-12 text-indigo-600 mb-4" />
                <CardTitle>真实用户</CardTitle>
                <CardDescription>
                  严格的用户验证机制，确保平台上都是真实、
                  认真寻找伴侣的用户
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* How it Works */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              如何开始？
            </h2>
            <p className="text-lg text-gray-600">
              简单三步，开启你的智能恋爱之旅
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                1
              </div>
              <h3 className="text-xl font-semibold mb-2">完善资料</h3>
              <p className="text-gray-600">
                详细填写个人信息、兴趣爱好和理想伴侣要求，
                让AI更好地了解你
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                2
              </div>
              <h3 className="text-xl font-semibold mb-2">AI分析匹配</h3>
              <p className="text-gray-600">
                AI深度分析你的人格特征，为你推荐最合适的潜在伴侣，
                并提供详细的匹配分析
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                3
              </div>
              <h3 className="text-xl font-semibold mb-2">开始聊天</h3>
              <p className="text-gray-600">
                双方互相喜欢后即可开始聊天，
                基于AI建议的话题开启美好的对话
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            准备好找到你的灵魂伴侣了吗？
          </h2>
          <p className="text-xl text-pink-100 mb-8">
            加入数千名用户，让AI帮你找到真正合适的人
          </p>
          <Link href="/auth/register">
            <Button size="lg" className="bg-white text-pink-600 hover:bg-gray-100">
              立即开始
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Heart className="w-6 h-6 text-pink-600" />
                <h3 className="text-lg font-bold">灵犀AI</h3>
              </div>
              <p className="text-gray-400">
                用AI技术重新定义恋爱匹配，
                为每个人找到最合适的伴侣。
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">产品</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">功能介绍</a></li>
                <li><a href="#" className="hover:text-white">定价</a></li>
                <li><a href="#" className="hover:text-white">安全保障</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">支持</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">帮助中心</a></li>
                <li><a href="#" className="hover:text-white">联系我们</a></li>
                <li><a href="#" className="hover:text-white">用户反馈</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">法律</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">隐私政策</a></li>
                <li><a href="#" className="hover:text-white">服务条款</a></li>
                <li><a href="#" className="hover:text-white">Cookie政策</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 灵犀AI. 保留所有权利。</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
