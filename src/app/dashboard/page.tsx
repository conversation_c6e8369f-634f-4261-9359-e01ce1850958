'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { MatchCard } from '@/components/MatchCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Users, MessageCircle, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DashboardPage() {
  const [matches, setMatches] = useState<any[]>([]);
  const [mutualMatches, setMutualMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'discover' | 'mutual'>('discover');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }
    await loadMatches();
  };

  const loadMatches = async () => {
    setLoading(true);
    try {
      // In a real app, you would call your API endpoints here
      // For now, we'll simulate some data
      const mockMatches = [
        {
          id: '1',
          compatibilityScore: 85,
          otherUser: {
            name: '小雨',
            age: 26,
            location: '北京',
            bio: '喜欢阅读和旅行，寻找有趣的灵魂。热爱文学，相信每一次旅行都是心灵的洗礼。',
            interests: ['阅读', '旅行', '摄影', '咖啡', '电影', '瑜伽', '音乐']
          },
          aiAnalysis: {
            explanation: '你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补。'
          },
          conversationSimulation: {
            conversation: [
              { speaker: 'user1', message: '你好！看到你也喜欢旅行，最近去过哪里？' },
              { speaker: 'user2', message: '你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？' },
              { speaker: 'user1', message: '云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。' },
              { speaker: 'user2', message: '哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。' }
            ],
            analysis: {
              conversationFlow: 88,
              valueAlignment: 82,
              communicationMatch: 85,
              overallCompatibility: 85,
              commonTopics: ['旅行', '摄影', '文化探索'],
              potentialConflicts: ['生活节奏差异']
            }
          },
          status: 'pending'
        },
        {
          id: '2',
          compatibilityScore: 78,
          otherUser: {
            name: '晓明',
            age: 28,
            location: '上海',
            bio: '程序员，热爱技术和创新，业余时间喜欢弹吉他和看科幻电影。',
            interests: ['编程', '科技', '吉他', '科幻电影', '游戏', '咖啡']
          },
          aiAnalysis: {
            explanation: '你们都有理性思维和对新技术的热情，在逻辑思考和问题解决方面很有共同语言。'
          },
          conversationSimulation: {
            conversation: [
              { speaker: 'user1', message: '看到你是程序员，最近在做什么有趣的项目吗？' },
              { speaker: 'user2', message: '最近在研究AI应用，你对人工智能怎么看？' }
            ],
            analysis: {
              conversationFlow: 75,
              valueAlignment: 80,
              communicationMatch: 78,
              overallCompatibility: 78,
              commonTopics: ['技术', '创新', '逻辑思维'],
              potentialConflicts: ['工作时间安排']
            }
          },
          status: 'pending'
        },
        {
          id: '3',
          compatibilityScore: 92,
          otherUser: {
            name: '梦琪',
            age: 24,
            location: '深圳',
            bio: '设计师，喜欢一切美好的事物。热爱艺术、美食和慢生活。',
            interests: ['设计', '艺术', '美食', '手工', '花艺', '茶道']
          },
          aiAnalysis: {
            explanation: '你们在审美和生活品味方面高度契合，都追求生活的美感和质量，有很强的精神共鸣。'
          },
          conversationSimulation: {
            conversation: [
              { speaker: 'user1', message: '你的设计作品一定很有创意，平时从哪里获得灵感？' },
              { speaker: 'user2', message: '我喜欢从自然和日常生活中寻找美，一朵花、一杯茶都能给我灵感。' }
            ],
            analysis: {
              conversationFlow: 95,
              valueAlignment: 90,
              communicationMatch: 92,
              overallCompatibility: 92,
              commonTopics: ['美学', '生活品味', '创意'],
              potentialConflicts: ['工作节奏差异']
            }
          },
          status: 'pending'
        }
      ];

      const mockMutualMatches = [
        {
          id: '2',
          compatibilityScore: 78,
          otherUser: {
            name: '晓明',
            age: 28,
            location: '上海',
            bio: '程序员，热爱技术和创新',
            interests: ['编程', '科技', '游戏', '音乐']
          },
          status: 'mutual_like',
          matchedAt: new Date().toISOString()
        }
      ];

      setMatches(mockMatches);
      setMutualMatches(mockMutualMatches);
    } catch (error) {
      console.error('Error loading matches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (matchId: string) => {
    try {
      // In a real app, you would call your API to update the match status
      console.log('Liked match:', matchId);
      
      // Remove from current matches and potentially add to mutual matches
      setMatches(prev => prev.filter(m => m.id !== matchId));
      
      // Show success message or handle mutual match
      alert('已发送喜欢！如果对方也喜欢你，你们就能开始聊天了。');
    } catch (error) {
      console.error('Error liking match:', error);
    }
  };

  const handlePass = async (matchId: string) => {
    try {
      // In a real app, you would call your API to update the match status
      console.log('Passed match:', matchId);
      
      setMatches(prev => prev.filter(m => m.id !== matchId));
    } catch (error) {
      console.error('Error passing match:', error);
    }
  };



  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold text-gray-900">灵犀AI</h1>
              <nav className="flex gap-4">
                <button
                  onClick={() => setActiveTab('discover')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === 'discover'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  发现
                </button>
                <button
                  onClick={() => setActiveTab('mutual')}
                  className={`px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                    activeTab === 'mutual'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Heart className="w-4 h-4" />
                  互相喜欢
                  {mutualMatches.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {mutualMatches.length}
                    </Badge>
                  )}
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/profile">
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  设置
                </Button>
              </Link>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                退出
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'discover' && (
          <div>
            <div className="mb-8 text-center">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent mb-3">
                ✨ 今日推荐
              </h2>
              <p className="text-gray-600 text-lg">基于AI深度分析为您精心挑选的匹配对象</p>
            </div>

            {matches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">暂无新的推荐</CardTitle>
                  <CardDescription>
                    请完善您的个人资料，我们会为您推荐更合适的对象
                  </CardDescription>
                  <Link href="/profile">
                    <Button className="mt-4">完善资料</Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {matches.map((match) => (
                  <MatchCard
                    key={match.id}
                    match={match}
                    onLike={handleLike}
                    onPass={handlePass}

                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'mutual' && (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">互相喜欢</h2>
              <p className="text-gray-600">你们互相喜欢，可以开始聊天了！</p>
            </div>

            {mutualMatches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">还没有互相喜欢的对象</CardTitle>
                  <CardDescription>
                    继续浏览推荐，找到心仪的人吧！
                  </CardDescription>
                  <Button 
                    className="mt-4"
                    onClick={() => setActiveTab('discover')}
                  >
                    去发现
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {mutualMatches.map((match) => (
                  <Card key={match.id} className="w-full max-w-md mx-auto">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-xl">{match.otherUser.name}</CardTitle>
                          <CardDescription>
                            {match.otherUser.age}岁 · {match.otherUser.location}
                          </CardDescription>
                        </div>
                        <Badge className="bg-pink-100 text-pink-800">
                          互相喜欢
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 mb-4">{match.otherUser.bio}</p>
                      <Button className="w-full flex items-center gap-2">
                        <MessageCircle className="w-4 h-4" />
                        开始聊天
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </main>


    </div>
  );
}
