'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { MatchCard } from '@/components/MatchCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Eye, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { QuotaLimitDialog } from '@/components/ui/quota-limit-dialog';

export default function DashboardPage() {
  const [matches, setMatches] = useState<any[]>([]);
  const [mutualMatches, setMutualMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState<'discover' | 'mutual'>('discover');
  const [showQuotaDialog, setShowQuotaDialog] = useState(false);
  const [quotaLimitInfo, setQuotaLimitInfo] = useState<any>(null);
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }
    await loadMatches();
  };

  const loadMatches = async () => {
    setLoading(true);
    try {
      // 获取用户的匹配记录
      const response = await fetch('/api/matches');
      if (response.ok) {
        const data = await response.json();
        const allMatches = data.matches || [];

        // 分离待处理的匹配和互相喜欢的匹配
        const pendingMatches = allMatches.filter((match: any) => match.status === 'pending');
        const mutualMatches = allMatches.filter((match: any) => match.status === 'mutual_like');

        setMatches(pendingMatches);
        setMutualMatches(mutualMatches);
      } else {
        console.error('Failed to load matches');
        // 如果API失败，暂时显示空数组
        setMatches([]);
        setMutualMatches([]);
      }
    } catch (error) {
      console.error('Error loading matches:', error);
      // 如果出错，暂时显示空数组
      setMatches([]);
      setMutualMatches([]);
    } finally {
      setLoading(false);
    }
  };

  const generateNewMatch = async () => {
    setIsGenerating(true);
    setProgress(0);

    try {
      // 模拟进度条
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 15;
        });
      }, 200);

      // 生成新匹配
      const response = await fetch('/api/matches?generate_daily=true');

      clearInterval(progressInterval);
      setProgress(100);

      if (response.ok) {
        const data = await response.json();

        if (data.matches && data.matches.length > 0) {
          // 找到最新的匹配（假设是数组中的最后一个）
          const newMatch = data.matches[data.matches.length - 1];

          // 等待一下让用户看到100%进度
          setTimeout(() => {
            router.push(`/match/${newMatch.id}`);
          }, 500);
        } else {
          alert('暂时没有新的匹配对象，请稍后再试');
          setIsGenerating(false);
          setProgress(0);
        }
      } else {
        const errorData = await response.json();

        if (errorData.error === 'DAILY_LIMIT_EXCEEDED') {
          const limitInfo = errorData.limitInfo;
          setQuotaLimitInfo(limitInfo);
          setShowQuotaDialog(true);
        } else {
          alert(errorData.message || '生成匹配失败，请重试');
        }

        setIsGenerating(false);
        setProgress(0);
      }
    } catch (error) {
      console.error('Error generating new match:', error);
      alert('网络错误，请重试');
      setIsGenerating(false);
      setProgress(0);
    }
  };

  const handleLike = async (matchId: string) => {
    try {
      console.log('Sending like request for match:', matchId);

      const response = await fetch(`/api/matches/${matchId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ liked: true }),
      });

      console.log('Response status:', response.status);
      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (response.ok) {
        console.log('Successfully liked match:', matchId);
        // 重新加载匹配数据以更新状态
        await loadMatches();
        alert('已发送喜欢！如果对方也喜欢你，你们就能开始聊天了。');
      } else {
        console.error('Failed to like match:', responseData);
        alert('操作失败，请重试');
      }
    } catch (error) {
      console.error('Error liking match:', error);
      alert('网络错误，请重试');
    }
  };

  const handlePass = async (matchId: string) => {
    try {
      console.log('Sending pass request for match:', matchId);

      const response = await fetch(`/api/matches/${matchId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ liked: false }),
      });

      console.log('Response status:', response.status);
      const responseData = await response.json();
      console.log('Response data:', responseData);

      if (response.ok) {
        console.log('Successfully passed match:', matchId);
        // 重新加载匹配数据以更新状态
        await loadMatches();
      } else {
        console.error('Failed to pass match:', responseData);
        alert('操作失败，请重试');
      }
    } catch (error) {
      console.error('Error passing match:', error);
      alert('网络错误，请重试');
    }
  };



  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
                ✨ 灵犀AI
              </h1>
              <nav className="flex gap-4">
                <button
                  onClick={() => setActiveTab('discover')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === 'discover'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  发现
                </button>
                <button
                  onClick={() => setActiveTab('mutual')}
                  className={`px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                    activeTab === 'mutual'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Heart className="w-4 h-4" />
                  互相喜欢
                  {mutualMatches.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {mutualMatches.length}
                    </Badge>
                  )}
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/profile">
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  设置
                </Button>
              </Link>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                退出
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* 发起匹配区域 - 只在发现页面显示 */}
        {activeTab === 'discover' && (
          <div className="text-center mb-12">
            <div className="mb-8">
              <h2 className="text-4xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent mb-4">
                🎯 发现你的灵魂伴侣
              </h2>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                基于AI深度分析，为您精心匹配最合适的对象。每次匹配都是一次心动的可能。
              </p>
            </div>

            {/* 发起匹配按钮和进度条 */}
            <div className="max-w-md mx-auto">
              {!isGenerating ? (
                <Button
                  onClick={generateNewMatch}
                  size="lg"
                  className="w-full h-14 text-lg bg-gradient-to-r from-pink-500 to-blue-500 hover:from-pink-600 hover:to-blue-600 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Heart className="w-6 h-6 mr-2" />
                  开始匹配
                </Button>
              ) : (
                <div className="space-y-4">
                  <div className="text-lg font-medium text-gray-700">
                    AI正在为您寻找完美匹配...
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-pink-500 to-blue-500 h-3 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {progress < 30 && "分析您的个人资料..."}
                    {progress >= 30 && progress < 60 && "寻找潜在匹配..."}
                    {progress >= 60 && progress < 90 && "计算兼容性分数..."}
                    {progress >= 90 && "生成深度分析报告..."}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 发现标签页内容 */}
        {activeTab === 'discover' && (
          <div>
            {matches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">暂无新的推荐</CardTitle>
                  <CardDescription>
                    点击上方"开始匹配"按钮，让AI为您寻找完美匹配！
                  </CardDescription>
                </CardContent>
              </Card>
            ) : (
              <div>
                <div className="mb-8 text-center">
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">
                    📋 待处理的匹配
                  </h3>
                  <p className="text-gray-600">
                    这些是为您推荐的匹配对象，快来看看吧！
                  </p>
                </div>
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {matches.map((match) => (
                    <MatchCard
                      key={match.id}
                      match={match}
                      onLike={handleLike}
                      onPass={handlePass}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 互相喜欢标签页内容 */}
        {activeTab === 'mutual' && (
          <div>
            {mutualMatches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">还没有互相喜欢的对象</CardTitle>
                  <CardDescription>
                    继续在发现页面寻找匹配，找到心仪的人吧！
                  </CardDescription>
                  <Button
                    className="mt-4"
                    onClick={() => setActiveTab('discover')}
                  >
                    去发现
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div>
                <div className="mb-8 text-center">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-pink-600 to-red-600 bg-clip-text text-transparent mb-3">
                    💕 互相喜欢
                  </h3>
                  <p className="text-gray-600 text-lg">你们互相喜欢，可以联系对方了！</p>
                </div>

                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                  {mutualMatches.map((match) => (
                  <Card key={match.id} className="w-full max-w-md mx-auto border-pink-200 bg-gradient-to-br from-pink-50 to-red-50">
                    <CardHeader className="pb-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center gap-3">
                          <div className="w-14 h-14 bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg">
                            {match.otherUser?.name?.charAt(0)}
                          </div>
                          <div>
                            <CardTitle className="text-xl font-bold text-gray-800">{match.otherUser.name}</CardTitle>
                            <CardDescription className="text-gray-600 font-medium">
                              {match.otherUser.age}岁 · {match.otherUser.location}
                            </CardDescription>
                          </div>
                        </div>
                        <Badge className="bg-gradient-to-r from-pink-500 to-red-500 text-white">
                          💕 互相喜欢
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {match.otherUser?.bio && (
                        <div className="bg-white/70 rounded-lg p-3 border-l-4 border-pink-400">
                          <p className="text-gray-700 leading-relaxed italic">"{match.otherUser.bio}"</p>
                        </div>
                      )}

                      {/* 联系方式 - 只有互相喜欢才显示 */}
                      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                        <h4 className="font-semibold mb-2 text-green-700 flex items-center gap-2">
                          <span className="text-lg">📧</span>
                          联系方式
                        </h4>
                        <div className="text-sm text-gray-700">
                          <p className="font-medium">邮箱：{match.otherUser.email}</p>
                          <p className="text-xs text-gray-500 mt-1">你们互相喜欢，现在可以联系对方了！</p>
                        </div>
                      </div>

                      <Link href={`/match/${match.id}`} className="block">
                        <Button
                          variant="outline"
                          className="w-full flex items-center gap-2 bg-white/70 hover:bg-white border-pink-200 hover:border-pink-300 transition-all duration-300"
                        >
                          <Eye className="w-4 h-4 text-pink-600" />
                          <span className="text-pink-700 font-medium">回顾匹配分析</span>
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </main>

      {/* 额度限制对话框 */}
      {quotaLimitInfo && (
        <QuotaLimitDialog
          isOpen={showQuotaDialog}
          onClose={() => setShowQuotaDialog(false)}
          hoursUntilReset={quotaLimitInfo.hoursUntilReset}
          nextResetTime={quotaLimitInfo.nextResetTime}
        />
      )}
    </div>
  );
}
