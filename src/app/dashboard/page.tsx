'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';
import { MatchCard } from '@/components/MatchCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Users, MessageCircle, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function DashboardPage() {
  const [matches, setMatches] = useState<any[]>([]);
  const [mutualMatches, setMutualMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'discover' | 'mutual'>('discover');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }
    await loadMatches();
  };

  const loadMatches = async () => {
    setLoading(true);
    try {
      // 获取用户的匹配记录
      const response = await fetch('/api/matches');
      if (response.ok) {
        const data = await response.json();
        const allMatches = data.matches || [];

        // 分离待处理的匹配和互相喜欢的匹配
        const pendingMatches = allMatches.filter((match: any) => match.status === 'pending');
        const mutualMatches = allMatches.filter((match: any) => match.status === 'mutual_like');

        setMatches(pendingMatches);
        setMutualMatches(mutualMatches);
      } else {
        console.error('Failed to load matches');
        // 如果API失败，暂时显示空数组
        setMatches([]);
        setMutualMatches([]);
      }
    } catch (error) {
      console.error('Error loading matches:', error);
      // 如果出错，暂时显示空数组
      setMatches([]);
      setMutualMatches([]);
    } finally {
      setLoading(false);
    }
  };

  const generateDailyMatches = async () => {
    setLoading(true);
    try {
      // 生成每日匹配
      const response = await fetch('/api/matches?generate_daily=true');
      if (response.ok) {
        const data = await response.json();
        const allMatches = data.matches || [];

        // 分离待处理的匹配和互相喜欢的匹配
        const pendingMatches = allMatches.filter((match: any) => match.status === 'pending');
        const mutualMatches = allMatches.filter((match: any) => match.status === 'mutual_like');

        setMatches(pendingMatches);
        setMutualMatches(mutualMatches);
      } else {
        console.error('Failed to generate daily matches');
      }
    } catch (error) {
      console.error('Error generating daily matches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (matchId: string) => {
    try {
      // In a real app, you would call your API to update the match status
      console.log('Liked match:', matchId);
      
      // Remove from current matches and potentially add to mutual matches
      setMatches(prev => prev.filter(m => m.id !== matchId));
      
      // Show success message or handle mutual match
      alert('已发送喜欢！如果对方也喜欢你，你们就能开始聊天了。');
    } catch (error) {
      console.error('Error liking match:', error);
    }
  };

  const handlePass = async (matchId: string) => {
    try {
      // In a real app, you would call your API to update the match status
      console.log('Passed match:', matchId);
      
      setMatches(prev => prev.filter(m => m.id !== matchId));
    } catch (error) {
      console.error('Error passing match:', error);
    }
  };



  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/auth/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold text-gray-900">灵犀AI</h1>
              <nav className="flex gap-4">
                <button
                  onClick={() => setActiveTab('discover')}
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === 'discover'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  发现
                </button>
                <button
                  onClick={() => setActiveTab('mutual')}
                  className={`px-3 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                    activeTab === 'mutual'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Heart className="w-4 h-4" />
                  互相喜欢
                  {mutualMatches.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {mutualMatches.length}
                    </Badge>
                  )}
                </button>
              </nav>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/profile">
                <Button variant="outline" size="sm">
                  <Settings className="w-4 h-4 mr-2" />
                  设置
                </Button>
              </Link>
              <Button variant="outline" size="sm" onClick={handleSignOut}>
                退出
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'discover' && (
          <div>
            <div className="mb-8 text-center">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent mb-3">
                ✨ 今日推荐
              </h2>
              <p className="text-gray-600 text-lg">基于AI深度分析为您精心挑选的匹配对象</p>
            </div>

            {matches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">暂无新的推荐</CardTitle>
                  <CardDescription>
                    请完善您的个人资料，或者生成今日的AI匹配推荐
                  </CardDescription>
                  <div className="flex gap-3 justify-center mt-4">
                    <Link href="/profile">
                      <Button variant="outline">完善资料</Button>
                    </Link>
                    <Button onClick={generateDailyMatches} disabled={loading}>
                      {loading ? '生成中...' : '🤖 发现新匹配'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                {matches.map((match) => (
                  <MatchCard
                    key={match.id}
                    match={match}
                    onLike={handleLike}
                    onPass={handlePass}

                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'mutual' && (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">互相喜欢</h2>
              <p className="text-gray-600">你们互相喜欢，可以开始聊天了！</p>
            </div>

            {mutualMatches.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">还没有互相喜欢的对象</CardTitle>
                  <CardDescription>
                    继续浏览推荐，找到心仪的人吧！
                  </CardDescription>
                  <Button 
                    className="mt-4"
                    onClick={() => setActiveTab('discover')}
                  >
                    去发现
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {mutualMatches.map((match) => (
                  <Card key={match.id} className="w-full max-w-md mx-auto">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-xl">{match.otherUser.name}</CardTitle>
                          <CardDescription>
                            {match.otherUser.age}岁 · {match.otherUser.location}
                          </CardDescription>
                        </div>
                        <Badge className="bg-pink-100 text-pink-800">
                          互相喜欢
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 mb-4">{match.otherUser.bio}</p>
                      <Button className="w-full flex items-center gap-2">
                        <MessageCircle className="w-4 h-4" />
                        开始聊天
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </main>


    </div>
  );
}
