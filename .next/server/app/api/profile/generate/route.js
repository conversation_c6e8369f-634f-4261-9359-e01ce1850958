/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/profile/generate/route";
exports.ids = ["app/api/profile/generate/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprofile%2Fgenerate%2Froute&page=%2Fapi%2Fprofile%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Fgenerate%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprofile%2Fgenerate%2Froute&page=%2Fapi%2Fprofile%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Fgenerate%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_profile_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/profile/generate/route.ts */ \"(rsc)/./src/app/api/profile/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/profile/generate/route\",\n        pathname: \"/api/profile/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/profile/generate/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/profile/generate/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_profile_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprofile%2Fgenerate%2Froute&page=%2Fapi%2Fprofile%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Fgenerate%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/profile/generate/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/profile/generate/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_gemini__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/gemini */ \"(rsc)/./src/lib/services/gemini.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: ()=>cookieStore\n        });\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { name, age, gender, location, interests } = await request.json();\n        // 验证必要字段\n        if (!name || !age || !gender) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '请先填写姓名、年龄和性别信息'\n            }, {\n                status: 400\n            });\n        }\n        // 生成AI个人资料\n        const generatedProfile = await generateProfileWithAI({\n            name,\n            age,\n            gender,\n            location,\n            interests\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(generatedProfile);\n    } catch (error) {\n        console.error('Error generating profile:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function generateProfileWithAI(userInfo) {\n    const prompt = `\n  基于以下用户信息，生成一个真实、吸引人的个人资料：\n\n  用户信息：\n  - 姓名：${userInfo.name}\n  - 年龄：${userInfo.age}\n  - 性别：${userInfo.gender}\n  - 位置：${userInfo.location || '未提供'}\n  - 兴趣爱好：${userInfo.interests?.join(', ') || '未提供'}\n\n  请生成以下四个字段的内容，要求：\n  1. 内容要真实可信，不要过于夸张\n  2. 符合中国文化背景和表达习惯\n  3. 根据年龄和性别调整语言风格\n  4. 每个字段控制在合适的长度\n\n  返回JSON格式：\n  {\n    \"bio\": \"个人简介（50-100字，简洁介绍自己）\",\n    \"selfDescription\": \"自我描述（100-200字，详细描述性格、价值观、生活方式等）\",\n    \"lookingFor\": \"寻找对象（80-150字，描述理想伴侣的特质和期望）\",\n    \"relationshipGoals\": \"感情目标（50-100字，说明希望建立什么样的关系）\"\n  }\n\n  示例风格参考：\n  - 如果是年轻人（20-30岁）：语言活泼一些，提到学习、工作、兴趣爱好\n  - 如果是中年人（30-40岁）：语言成熟稳重，提到事业、家庭、人生规划\n  - 男性：可以提到责任感、上进心、兴趣爱好\n  - 女性：可以提到温柔、独立、生活品质\n\n  请确保内容积极正面，避免负面表达。\n  `;\n    try {\n        const result = await _lib_services_gemini__WEBPACK_IMPORTED_MODULE_3__.GeminiService.generateText(prompt);\n        // 尝试解析JSON\n        const cleanedResult = result.replace(/```json\\n?|\\n?```/g, '').trim();\n        const parsedResult = JSON.parse(cleanedResult);\n        // 验证返回的字段\n        if (!parsedResult.bio || !parsedResult.selfDescription || !parsedResult.lookingFor || !parsedResult.relationshipGoals) {\n            throw new Error('AI生成的内容格式不完整');\n        }\n        return parsedResult;\n    } catch (error) {\n        console.error('Error parsing AI response:', error);\n        // 如果AI生成失败，返回默认示例\n        return getDefaultProfileExample(userInfo);\n    }\n}\nfunction getDefaultProfileExample(userInfo) {\n    const { name, age, gender } = userInfo;\n    // 根据性别和年龄生成不同的默认示例\n    if (gender === 'male') {\n        return {\n            bio: `我是${name}，${age}岁，一个积极向上的人。喜欢探索新事物，享受生活中的美好时刻。`,\n            selfDescription: `我是一个比较随和的人，喜欢和朋友聊天，也享受独处的时光。工作上比较认真负责，生活中喜欢尝试新的体验。我相信真诚和善良是最重要的品质，希望能遇到志同道合的人一起分享生活的点点滴滴。`,\n            lookingFor: `希望遇到一个善良、有趣的女生，我们可以一起聊天、一起探索这个世界。不需要完全相同的兴趣爱好，但希望我们能互相理解和支持。`,\n            relationshipGoals: `希望能建立一段真诚、稳定的关系，从朋友开始，慢慢了解彼此，看看是否适合走得更远。`\n        };\n    } else {\n        return {\n            bio: `我是${name}，${age}岁，一个热爱生活的女生。喜欢美好的事物，相信生活中处处都有小确幸。`,\n            selfDescription: `我是一个比较温和的人，喜欢和朋友分享生活中的趣事，也很享受安静的独处时光。我觉得保持好奇心很重要，总是愿意尝试新的事物。我重视真诚的交流，希望能遇到一个能够理解我、支持我的人。`,\n            lookingFor: `希望遇到一个成熟、有责任心的男生，我们可以一起成长，一起面对生活中的挑战。希望他有自己的兴趣爱好，也能尊重我的选择。`,\n            relationshipGoals: `希望能找到一个可以长期相伴的人，我们可以从了解开始，慢慢建立深厚的感情基础。`\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/profile/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/gemini.ts":
/*!************************************!*\
  !*** ./src/lib/services/gemini.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    baseURL: 'https://openrouter.ai/api/v1',\n    apiKey: process.env.OPENROUTER_API_KEY\n});\nclass GeminiService {\n    static{\n        this.model = 'google/gemini-2.5-flash-preview-05-20';\n    }\n    static async generatePersonalitySummary(userProfile) {\n        const prompt = `\n    基于以下用户信息，生成一个详细的人格摘要：\n\n    基本信息：\n    - 姓名：${userProfile.name}\n    - 年龄：${userProfile.age}\n    - 性别：${userProfile.gender}\n    - 所在地：${userProfile.location}\n\n    个人描述：\n    - 个人简介：${userProfile.bio}\n    - 自我描述：${userProfile.selfDescription}\n    - 兴趣爱好：${userProfile.interests?.join(', ')}\n    - 寻找对象：${userProfile.lookingFor}\n    - 感情目标：${userProfile.relationshipGoals}\n\n    请分析这个人的：\n    1. 性格特征（外向/内向、开放性、责任心等）\n    2. 价值观和生活态度\n    3. 社交偏好和沟通风格\n    4. 感情需求和期望\n    5. 生活方式和兴趣匹配度\n\n    请用中文回答，格式为结构化的JSON，包含以下字段：\n    {\n      \"personalityTraits\": {\n        \"extraversion\": 0-100,\n        \"openness\": 0-100,\n        \"conscientiousness\": 0-100,\n        \"agreeableness\": 0-100,\n        \"neuroticism\": 0-100\n      },\n      \"values\": [\"价值观1\", \"价值观2\", ...],\n      \"communicationStyle\": \"沟通风格描述\",\n      \"relationshipNeeds\": \"感情需求描述\",\n      \"lifestyle\": \"生活方式描述\",\n      \"summary\": \"整体人格摘要\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error generating personality summary:', error.stack);\n            throw error;\n        }\n    }\n    static async simulateConversation(user1Profile, user2Profile) {\n        const prompt = `\n    模拟两个用户之间的对话，分析他们的兼容性：\n\n    用户1 - ${user1Profile.name}：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2 - ${user2Profile.name}：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请模拟他们第一次见面时的对话（8-15轮对话），并分析：\n    1. 对话流畅度\n    2. 共同话题\n    3. 价值观契合度\n    4. 沟通风格匹配\n    5. 潜在冲突点\n\n    重要：在对话中使用真实的用户名称（${user1Profile.name} 和 ${user2Profile.name}），而不是 \"user1\" 或 \"user2\"。\n\n    返回JSON格式：\n    {\n      \"conversation\": [\n        {\"speaker\": \"${user1Profile.name}\", \"message\": \"对话内容\"},\n        {\"speaker\": \"${user2Profile.name}\", \"message\": \"对话内容\"},\n        ...\n      ],\n      \"analysis\": {\n        \"conversationFlow\": 0-100,\n        \"commonTopics\": [\"话题1\", \"话题2\", ...],\n        \"valueAlignment\": 0-100,\n        \"communicationMatch\": 0-100,\n        \"potentialConflicts\": [\"冲突点1\", \"冲突点2\", ...],\n        \"overallCompatibility\": 0-100\n      }\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error simulating conversation:', error);\n            throw error;\n        }\n    }\n    static async calculateCompatibilityScore(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，计算他们的兼容性分数（0-100）：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    考虑因素：\n    1. 性格互补性（30%）\n    2. 价值观一致性（25%）\n    3. 兴趣爱好重叠（20%）\n    4. 生活方式匹配（15%）\n    5. 感情目标一致（10%）\n\n    只返回一个0-100之间的数字。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            const text = completion.choices[0].message.content || '';\n            const score = parseInt(text.match(/\\d+/)?.[0] || '0');\n            return Math.min(Math.max(score, 0), 100);\n        } catch (error) {\n            console.error('Error calculating compatibility score:', error);\n            return 0;\n        }\n    }\n    static async generateMatchExplanation(user1Profile, user2Profile, score) {\n        const prompt = `\n    解释为什么这两个用户的兼容性分数是${score}分：\n\n    用户1：${user1Profile.name}\n    用户2：${user2Profile.name}\n\n    请提供：\n    1. 匹配的优势（为什么他们适合）\n    2. 潜在的挑战（需要注意的地方）\n    3. 建议的相处方式\n    4. 发展前景预测\n\n    用温暖、专业的语调，给出建设性的建议。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            return completion.choices[0].message.content || '暂时无法生成匹配解释，请稍后重试。';\n        } catch (error) {\n            console.error('Error generating match explanation:', error);\n            return '暂时无法生成匹配解释，请稍后重试。';\n        }\n    }\n    static async generateComprehensiveAnalysis(user1Profile, user2Profile, score) {\n        const prompt = `\n    基于以下两个用户的资料，生成详细的匹配分析：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    兼容性分数：${score}分\n\n    请生成结构化的分析报告，包含：\n    1. 详细的匹配解释\n    2. 3-5个匹配优势\n    3. 2-3个需要注意的挑战\n    4. 3-5个建议的聊天话题\n\n    返回JSON格式：\n    {\n      \"explanation\": \"详细的匹配解释文本\",\n      \"strengths\": [\"优势1\", \"优势2\", \"优势3\"],\n      \"challenges\": [\"挑战1\", \"挑战2\"],\n      \"suggestions\": [\"话题1\", \"话题2\", \"话题3\"]\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a structured response\n            return {\n                explanation: '基于双方的资料分析，你们在多个方面都有很好的契合度。',\n                strengths: [\n                    '价值观契合',\n                    '兴趣相投',\n                    '性格互补'\n                ],\n                challenges: [\n                    '需要更多了解',\n                    '沟通方式磨合'\n                ],\n                suggestions: [\n                    '分享兴趣爱好',\n                    '聊聊人生目标',\n                    '交流价值观'\n                ]\n            };\n        } catch (error) {\n            console.error('Error generating comprehensive analysis:', error);\n            // Return fallback data\n            return {\n                explanation: '暂时无法生成详细分析，请稍后重试。',\n                strengths: [\n                    '等待AI分析'\n                ],\n                challenges: [\n                    '等待AI分析'\n                ],\n                suggestions: [\n                    '等待AI分析'\n                ]\n            };\n        }\n    }\n    static async generateDatePlan(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，为他们设计一个完美的约会计划：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请设计一个考虑双方兴趣爱好、性格特点和偏好的约会计划。包含：\n    1. 约会主题和理念\n    2. 具体的约会活动安排（时间线）\n    3. 推荐的地点类型\n    4. 注意事项和建议\n    5. 备选方案\n\n    返回JSON格式：\n    {\n      \"theme\": \"约会主题\",\n      \"concept\": \"约会理念描述\",\n      \"timeline\": [\n        {\n          \"time\": \"时间段\",\n          \"activity\": \"活动内容\",\n          \"location\": \"地点类型\",\n          \"reason\": \"选择理由\"\n        }\n      ],\n      \"recommendations\": {\n        \"locations\": [\"推荐地点1\", \"推荐地点2\", ...],\n        \"tips\": [\"建议1\", \"建议2\", ...],\n        \"alternatives\": [\"备选方案1\", \"备选方案2\", ...]\n      },\n      \"budget\": \"预算建议\",\n      \"duration\": \"约会时长\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a basic date plan\n            return {\n                theme: \"轻松愉快的初次约会\",\n                concept: \"选择轻松的环境，让双方都能感到舒适，有充分的交流机会。\",\n                timeline: [\n                    {\n                        time: \"下午2:00-3:30\",\n                        activity: \"咖啡厅聊天\",\n                        location: \"安静的咖啡厅\",\n                        reason: \"轻松的环境有利于深入交流\"\n                    },\n                    {\n                        time: \"下午3:30-5:00\",\n                        activity: \"公园散步\",\n                        location: \"附近的公园\",\n                        reason: \"自然环境能缓解紧张情绪\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"星巴克\",\n                        \"当地特色咖啡厅\",\n                        \"公园\",\n                        \"美术馆\"\n                    ],\n                    tips: [\n                        \"保持轻松的心态\",\n                        \"准备一些有趣的话题\",\n                        \"注意倾听\"\n                    ],\n                    alternatives: [\n                        \"如果天气不好可以选择室内活动\",\n                        \"可以根据聊天情况延长或缩短时间\"\n                    ]\n                },\n                budget: \"100-200元\",\n                duration: \"2-3小时\"\n            };\n        } catch (error) {\n            console.error('Error generating date plan:', error);\n            // Return fallback data\n            return {\n                theme: \"经典约会\",\n                concept: \"简单而美好的相遇。\",\n                timeline: [\n                    {\n                        time: \"下午\",\n                        activity: \"咖啡约会\",\n                        location: \"咖啡厅\",\n                        reason: \"轻松愉快的环境\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"咖啡厅\"\n                    ],\n                    tips: [\n                        \"保持自然\"\n                    ],\n                    alternatives: [\n                        \"灵活调整\"\n                    ]\n                },\n                budget: \"适中\",\n                duration: \"2小时\"\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/gemini.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprofile%2Fgenerate%2Froute&page=%2Fapi%2Fprofile%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprofile%2Fgenerate%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();