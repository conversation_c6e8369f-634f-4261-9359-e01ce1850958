/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/matches/[id]/route";
exports.ids = ["app/api/matches/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/matches/[id]/route.ts */ \"(rsc)/./src/app/api/matches/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/matches/[id]/route\",\n        pathname: \"/api/matches/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/matches/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/matches/[id]/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/matches/[id]/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_matching__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/matching */ \"(rsc)/./src/lib/services/matching.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n\n\n\n\n\n\n\nasync function GET(request, context) {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: ()=>cookieStore\n        });\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const params = await context.params;\n        const matchId = params.id;\n        // 获取匹配记录\n        const [match] = await _lib_db__WEBPACK_IMPORTED_MODULE_4__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.matches.id, matchId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.matches.user1Id, user.id), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.matches.user2Id, user.id)))).limit(1);\n        if (!match) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Match not found'\n            }, {\n                status: 404\n            });\n        }\n        // 获取对方用户信息\n        const otherUserId = match.user1Id === user.id ? match.user2Id : match.user1Id;\n        const [otherUser] = await _lib_db__WEBPACK_IMPORTED_MODULE_4__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.users.id, otherUserId)).limit(1);\n        const [otherUserProfile] = await _lib_db__WEBPACK_IMPORTED_MODULE_4__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.userProfiles).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_5__.userProfiles.userId, otherUserId)).limit(1);\n        const enrichedMatch = {\n            ...match,\n            otherUser,\n            otherUserProfile\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            match: enrichedMatch\n        });\n    } catch (error) {\n        console.error('Error fetching match:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, context) {\n    try {\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_1__.createRouteHandlerClient)({\n            cookies: ()=>cookieStore\n        });\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { liked } = await request.json();\n        const params = await context.params;\n        const matchId = params.id;\n        const updatedMatch = await _lib_services_matching__WEBPACK_IMPORTED_MODULE_3__.MatchingService.updateMatchStatus(matchId, user.id, liked);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            match: updatedMatch\n        });\n    } catch (error) {\n        console.error('Error updating match status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/matches/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n\n\n\n// Create the connection\nconst connectionString = process.env.DATABASE_URL;\nlet db;\nif (connectionString) {\n    // Create postgres client\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsd0JBQXdCO0FBQ3hCLE1BQU1HLG1CQUFtQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBRWpELElBQUlDO0FBRUosSUFBSUosa0JBQWtCO0lBQ3BCLHlCQUF5QjtJQUN6QixNQUFNSyxTQUFTUCxvREFBUUEsQ0FBQ0Usa0JBQWtCO1FBQ3hDTSxLQUFLO1FBQ0xDLGNBQWM7UUFDZEMsaUJBQWlCO0lBQ25CO0lBRUEsMEJBQTBCO0lBQzFCSixLQUFLUCxnRUFBT0EsQ0FBQ1EsUUFBUTtRQUFFTixNQUFNQSxzQ0FBQUE7SUFBQztBQUNoQyxPQUFPO0lBQ0wsdURBQXVEO0lBQ3ZELE1BQU1VLGFBQWEsQ0FBQztJQUNwQkwsS0FBS1AsZ0VBQU9BLENBQUNZLFlBQVk7UUFBRVYsTUFBTUEsc0NBQUFBO0lBQUM7QUFDcEM7QUFFYztBQUVkLHVDQUF1QztBQUNkIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvZGIvaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZHJpenpsZSB9IGZyb20gJ2RyaXp6bGUtb3JtL3Bvc3RncmVzLWpzJztcbmltcG9ydCBwb3N0Z3JlcyBmcm9tICdwb3N0Z3Jlcyc7XG5pbXBvcnQgKiBhcyBzY2hlbWEgZnJvbSAnLi9zY2hlbWEnO1xuXG4vLyBDcmVhdGUgdGhlIGNvbm5lY3Rpb25cbmNvbnN0IGNvbm5lY3Rpb25TdHJpbmcgPSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkw7XG5cbmxldCBkYjogUmV0dXJuVHlwZTx0eXBlb2YgZHJpenpsZT47XG5cbmlmIChjb25uZWN0aW9uU3RyaW5nKSB7XG4gIC8vIENyZWF0ZSBwb3N0Z3JlcyBjbGllbnRcbiAgY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywge1xuICAgIG1heDogMSxcbiAgICBpZGxlX3RpbWVvdXQ6IDIwLFxuICAgIGNvbm5lY3RfdGltZW91dDogMTAsXG4gIH0pO1xuXG4gIC8vIENyZWF0ZSBkcml6emxlIGluc3RhbmNlXG4gIGRiID0gZHJpenpsZShjbGllbnQsIHsgc2NoZW1hIH0pO1xufSBlbHNlIHtcbiAgLy8gTW9jayBkYXRhYmFzZSBmb3IgYnVpbGQgdGltZSAtIGNyZWF0ZSBhIG1pbmltYWwgbW9ja1xuICBjb25zdCBtb2NrQ2xpZW50ID0ge30gYXMgYW55O1xuICBkYiA9IGRyaXp6bGUobW9ja0NsaWVudCwgeyBzY2hlbWEgfSk7XG59XG5cbmV4cG9ydCB7IGRiIH07XG5cbi8vIEV4cG9ydCBzY2hlbWEgZm9yIHVzZSBpbiBvdGhlciBmaWxlc1xuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiZGIiLCJjbGllbnQiLCJtYXgiLCJpZGxlX3RpbWVvdXQiLCJjb25uZWN0X3RpbWVvdXQiLCJtb2NrQ2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/gemini.ts":
/*!************************************!*\
  !*** ./src/lib/services/gemini.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    baseURL: 'https://openrouter.ai/api/v1',\n    apiKey: process.env.OPENROUTER_API_KEY\n});\nclass GeminiService {\n    static{\n        this.model = 'google/gemini-2.5-flash-preview-05-20';\n    }\n    static async generatePersonalitySummary(userProfile) {\n        const prompt = `\n    基于以下用户信息，生成一个详细的人格摘要：\n\n    基本信息：\n    - 姓名：${userProfile.name}\n    - 年龄：${userProfile.age}\n    - 性别：${userProfile.gender}\n    - 所在地：${userProfile.location}\n\n    个人描述：\n    - 个人简介：${userProfile.bio}\n    - 自我描述：${userProfile.selfDescription}\n    - 兴趣爱好：${userProfile.interests?.join(', ')}\n    - 寻找对象：${userProfile.lookingFor}\n    - 感情目标：${userProfile.relationshipGoals}\n\n    请分析这个人的：\n    1. 性格特征（外向/内向、开放性、责任心等）\n    2. 价值观和生活态度\n    3. 社交偏好和沟通风格\n    4. 感情需求和期望\n    5. 生活方式和兴趣匹配度\n\n    请用中文回答，格式为结构化的JSON，包含以下字段：\n    {\n      \"personalityTraits\": {\n        \"extraversion\": 0-100,\n        \"openness\": 0-100,\n        \"conscientiousness\": 0-100,\n        \"agreeableness\": 0-100,\n        \"neuroticism\": 0-100\n      },\n      \"values\": [\"价值观1\", \"价值观2\", ...],\n      \"communicationStyle\": \"沟通风格描述\",\n      \"relationshipNeeds\": \"感情需求描述\",\n      \"lifestyle\": \"生活方式描述\",\n      \"summary\": \"整体人格摘要\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error generating personality summary:', error.stack);\n            throw error;\n        }\n    }\n    static async simulateConversation(user1Profile, user2Profile) {\n        const prompt = `\n    模拟两个用户之间的对话，分析他们的兼容性：\n\n    用户1 - ${user1Profile.name}：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2 - ${user2Profile.name}：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请模拟他们第一次见面时的对话（5-8轮对话），并分析：\n    1. 对话流畅度\n    2. 共同话题\n    3. 价值观契合度\n    4. 沟通风格匹配\n    5. 潜在冲突点\n\n    重要：在对话中使用真实的用户名称（${user1Profile.name} 和 ${user2Profile.name}），而不是 \"user1\" 或 \"user2\"。\n\n    返回JSON格式：\n    {\n      \"conversation\": [\n        {\"speaker\": \"${user1Profile.name}\", \"message\": \"对话内容\"},\n        {\"speaker\": \"${user2Profile.name}\", \"message\": \"对话内容\"},\n        ...\n      ],\n      \"analysis\": {\n        \"conversationFlow\": 0-100,\n        \"commonTopics\": [\"话题1\", \"话题2\", ...],\n        \"valueAlignment\": 0-100,\n        \"communicationMatch\": 0-100,\n        \"potentialConflicts\": [\"冲突点1\", \"冲突点2\", ...],\n        \"overallCompatibility\": 0-100\n      }\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error simulating conversation:', error);\n            throw error;\n        }\n    }\n    static async calculateCompatibilityScore(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，计算他们的兼容性分数（0-100）：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    考虑因素：\n    1. 性格互补性（30%）\n    2. 价值观一致性（25%）\n    3. 兴趣爱好重叠（20%）\n    4. 生活方式匹配（15%）\n    5. 感情目标一致（10%）\n\n    只返回一个0-100之间的数字。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            const text = completion.choices[0].message.content || '';\n            const score = parseInt(text.match(/\\d+/)?.[0] || '0');\n            return Math.min(Math.max(score, 0), 100);\n        } catch (error) {\n            console.error('Error calculating compatibility score:', error);\n            return 0;\n        }\n    }\n    static async generateMatchExplanation(user1Profile, user2Profile, score) {\n        const prompt = `\n    解释为什么这两个用户的兼容性分数是${score}分：\n\n    用户1：${user1Profile.name}\n    用户2：${user2Profile.name}\n\n    请提供：\n    1. 匹配的优势（为什么他们适合）\n    2. 潜在的挑战（需要注意的地方）\n    3. 建议的相处方式\n    4. 发展前景预测\n\n    用温暖、专业的语调，给出建设性的建议。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            return completion.choices[0].message.content || '暂时无法生成匹配解释，请稍后重试。';\n        } catch (error) {\n            console.error('Error generating match explanation:', error);\n            return '暂时无法生成匹配解释，请稍后重试。';\n        }\n    }\n    static async generateComprehensiveAnalysis(user1Profile, user2Profile, score) {\n        const prompt = `\n    基于以下两个用户的资料，生成详细的匹配分析：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    兼容性分数：${score}分\n\n    请生成结构化的分析报告，包含：\n    1. 详细的匹配解释\n    2. 3-5个匹配优势\n    3. 2-3个需要注意的挑战\n    4. 3-5个建议的聊天话题\n\n    返回JSON格式：\n    {\n      \"explanation\": \"详细的匹配解释文本\",\n      \"strengths\": [\"优势1\", \"优势2\", \"优势3\"],\n      \"challenges\": [\"挑战1\", \"挑战2\"],\n      \"suggestions\": [\"话题1\", \"话题2\", \"话题3\"]\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a structured response\n            return {\n                explanation: '基于双方的资料分析，你们在多个方面都有很好的契合度。',\n                strengths: [\n                    '价值观契合',\n                    '兴趣相投',\n                    '性格互补'\n                ],\n                challenges: [\n                    '需要更多了解',\n                    '沟通方式磨合'\n                ],\n                suggestions: [\n                    '分享兴趣爱好',\n                    '聊聊人生目标',\n                    '交流价值观'\n                ]\n            };\n        } catch (error) {\n            console.error('Error generating comprehensive analysis:', error);\n            // Return fallback data\n            return {\n                explanation: '暂时无法生成详细分析，请稍后重试。',\n                strengths: [\n                    '等待AI分析'\n                ],\n                challenges: [\n                    '等待AI分析'\n                ],\n                suggestions: [\n                    '等待AI分析'\n                ]\n            };\n        }\n    }\n    static async generateDatePlan(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，为他们设计一个完美的约会计划：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请设计一个考虑双方兴趣爱好、性格特点和偏好的约会计划。包含：\n    1. 约会主题和理念\n    2. 具体的约会活动安排（时间线）\n    3. 推荐的地点类型\n    4. 注意事项和建议\n    5. 备选方案\n\n    返回JSON格式：\n    {\n      \"theme\": \"约会主题\",\n      \"concept\": \"约会理念描述\",\n      \"timeline\": [\n        {\n          \"time\": \"时间段\",\n          \"activity\": \"活动内容\",\n          \"location\": \"地点类型\",\n          \"reason\": \"选择理由\"\n        }\n      ],\n      \"recommendations\": {\n        \"locations\": [\"推荐地点1\", \"推荐地点2\", ...],\n        \"tips\": [\"建议1\", \"建议2\", ...],\n        \"alternatives\": [\"备选方案1\", \"备选方案2\", ...]\n      },\n      \"budget\": \"预算建议\",\n      \"duration\": \"约会时长\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a basic date plan\n            return {\n                theme: \"轻松愉快的初次约会\",\n                concept: \"选择轻松的环境，让双方都能感到舒适，有充分的交流机会。\",\n                timeline: [\n                    {\n                        time: \"下午2:00-3:30\",\n                        activity: \"咖啡厅聊天\",\n                        location: \"安静的咖啡厅\",\n                        reason: \"轻松的环境有利于深入交流\"\n                    },\n                    {\n                        time: \"下午3:30-5:00\",\n                        activity: \"公园散步\",\n                        location: \"附近的公园\",\n                        reason: \"自然环境能缓解紧张情绪\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"星巴克\",\n                        \"当地特色咖啡厅\",\n                        \"公园\",\n                        \"美术馆\"\n                    ],\n                    tips: [\n                        \"保持轻松的心态\",\n                        \"准备一些有趣的话题\",\n                        \"注意倾听\"\n                    ],\n                    alternatives: [\n                        \"如果天气不好可以选择室内活动\",\n                        \"可以根据聊天情况延长或缩短时间\"\n                    ]\n                },\n                budget: \"100-200元\",\n                duration: \"2-3小时\"\n            };\n        } catch (error) {\n            console.error('Error generating date plan:', error);\n            // Return fallback data\n            return {\n                theme: \"经典约会\",\n                concept: \"简单而美好的相遇。\",\n                timeline: [\n                    {\n                        time: \"下午\",\n                        activity: \"咖啡约会\",\n                        location: \"咖啡厅\",\n                        reason: \"轻松愉快的环境\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"咖啡厅\"\n                    ],\n                    tips: [\n                        \"保持自然\"\n                    ],\n                    alternatives: [\n                        \"灵活调整\"\n                    ]\n                },\n                budget: \"适中\",\n                duration: \"2小时\"\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/matching.ts":
/*!**************************************!*\
  !*** ./src/lib/services/matching.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchingService: () => (/* binding */ MatchingService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gemini */ \"(rsc)/./src/lib/services/gemini.ts\");\n\n\n\n\nclass MatchingService {\n    // 检查24小时内的匹配次数\n    static async checkDailyMatchLimit(userId) {\n        try {\n            // 计算24小时前的时间\n            const twentyFourHoursAgo = new Date();\n            twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);\n            // 查询24小时内用户创建的匹配数量\n            const recentMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.createdAt, twentyFourHoursAgo)));\n            const matchCount = recentMatches.length;\n            const maxDailyMatches = 3;\n            const canMatch = matchCount < maxDailyMatches;\n            const remainingMatches = Math.max(0, maxDailyMatches - matchCount);\n            // 计算下次重置时间（最早匹配的24小时后）\n            let nextResetTime = new Date();\n            if (recentMatches.length > 0) {\n                const earliestMatch = recentMatches.reduce((earliest, match)=>new Date(match.createdAt || new Date()) < new Date(earliest.createdAt || new Date()) ? match : earliest);\n                nextResetTime = new Date(earliestMatch.createdAt || new Date());\n                nextResetTime.setHours(nextResetTime.getHours() + 24);\n            }\n            return {\n                canMatch,\n                remainingMatches,\n                nextResetTime\n            };\n        } catch (error) {\n            console.error('Error checking daily match limit:', error);\n            // 如果检查失败，默认允许匹配\n            return {\n                canMatch: true,\n                remainingMatches: 3,\n                nextResetTime: new Date()\n            };\n        }\n    }\n    static async findPotentialMatches(userId, limit = 10) {\n        // Get current user's profile\n        const currentUser = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId)).limit(1);\n        if (!currentUser.length) {\n            throw new Error('User not found');\n        }\n        // Get all existing matches for current user (both as user1 and user2)\n        const existingMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user2Id, userId)));\n        // Extract all matched user IDs\n        const existingMatchIds = new Set();\n        existingMatches.forEach((match)=>{\n            if (match.user1Id === userId) {\n                existingMatchIds.add(match.user2Id);\n            } else {\n                existingMatchIds.add(match.user1Id);\n            }\n        });\n        // Get current user's gender for opposite gender matching\n        const currentUserGender = currentUser[0].gender;\n        const targetGender = currentUserGender === 'male' ? 'female' : 'male';\n        // Find all potential matches (excluding current user, already matched users, and same gender)\n        const allPotentialMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.ne)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.isActive, true), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.gender, targetGender) // Only match opposite gender\n        ));\n        // Filter out already matched users\n        const availableMatches = allPotentialMatches.filter((user)=>!existingMatchIds.has(user.id));\n        // Randomize the results to avoid always matching the same users\n        const shuffled = availableMatches.sort(()=>Math.random() - 0.5);\n        // Return limited number of matches\n        return shuffled.slice(0, limit);\n    }\n    static async createMatch(user1Id, user2Id) {\n        try {\n            // Get both users' profiles\n            const [user1Data, user2Data] = await Promise.all([\n                this.getUserWithProfile(user1Id),\n                this.getUserWithProfile(user2Id)\n            ]);\n            if (!user1Data || !user2Data) {\n                throw new Error('One or both users not found');\n            }\n            // Generate AI analysis\n            const [personalitySummary1, personalitySummary2] = await Promise.all([\n                _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generatePersonalitySummary({\n                    ...user1Data.user,\n                    ...user1Data.profile\n                }),\n                _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generatePersonalitySummary({\n                    ...user2Data.user,\n                    ...user2Data.profile\n                })\n            ]);\n            // Calculate compatibility score\n            const compatibilityScore = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.calculateCompatibilityScore({\n                ...user1Data.user,\n                ...user1Data.profile,\n                personalitySummary: personalitySummary1\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile,\n                personalitySummary: personalitySummary2\n            });\n            // Simulate conversation\n            const conversationSimulation = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.simulateConversation({\n                ...user1Data.user,\n                ...user1Data.profile,\n                personalitySummary: personalitySummary1\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile,\n                personalitySummary: personalitySummary2\n            });\n            // Generate comprehensive match analysis\n            const matchAnalysis = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generateComprehensiveAnalysis({\n                ...user1Data.user,\n                ...user1Data.profile\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile\n            }, compatibilityScore);\n            // Generate date plan\n            const datePlan = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generateDatePlan({\n                ...user1Data.user,\n                ...user1Data.profile\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile\n            });\n            // Create match record\n            const matchData = {\n                user1Id,\n                user2Id,\n                compatibilityScore,\n                aiAnalysis: {\n                    user1PersonalitySummary: personalitySummary1,\n                    user2PersonalitySummary: personalitySummary2,\n                    explanation: matchAnalysis.explanation,\n                    strengths: matchAnalysis.strengths,\n                    challenges: matchAnalysis.challenges,\n                    suggestions: matchAnalysis.suggestions,\n                    datePlan: datePlan\n                },\n                conversationSimulation,\n                status: 'pending'\n            };\n            const [match] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).values(matchData).returning();\n            return {\n                match,\n                user1: user1Data.user,\n                user2: user2Data.user,\n                compatibilityScore,\n                explanation: matchAnalysis.explanation,\n                conversationSimulation\n            };\n        } catch (error) {\n            console.error('Error creating match:', error);\n            throw error;\n        }\n    }\n    static async getUserMatches(userId) {\n        const userMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user2Id, userId)));\n        const enrichedMatches = await Promise.all(userMatches.map(async (match)=>{\n            const otherUserId = match.user1Id === userId ? match.user2Id : match.user1Id;\n            const otherUserData = await this.getUserWithProfile(otherUserId);\n            return {\n                ...match,\n                otherUser: otherUserData?.user,\n                otherUserProfile: otherUserData?.profile\n            };\n        }));\n        return enrichedMatches;\n    }\n    static async updateMatchStatus(matchId, userId, liked) {\n        const [match] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.id, matchId)).limit(1);\n        if (!match) {\n            throw new Error('Match not found');\n        }\n        const isUser1 = match.user1Id === userId;\n        const updateData = {};\n        if (isUser1) {\n            updateData.user1Liked = liked;\n            updateData.user1Viewed = true;\n        } else {\n            updateData.user2Liked = liked;\n            updateData.user2Viewed = true;\n        }\n        // Check if both users have liked each other\n        const otherUserLiked = isUser1 ? match.user2Liked : match.user1Liked;\n        if (liked && otherUserLiked) {\n            updateData.status = 'mutual_like';\n        } else if (!liked) {\n            updateData.status = 'rejected';\n        }\n        const [updatedMatch] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).set(updateData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.id, matchId)).returning();\n        return updatedMatch;\n    }\n    static async getUserWithProfile(userId) {\n        const [user] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId)).limit(1);\n        if (!user) return null;\n        const [profile] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles.userId, userId)).limit(1);\n        return {\n            user,\n            profile: profile || null\n        };\n    }\n    static async generateDailyMatches(userId) {\n        try {\n            // 改为一次只生成一个匹配，减少 token 消耗\n            const potentialMatches = await this.findPotentialMatches(userId, 1);\n            const matches = [];\n            for (const potentialMatch of potentialMatches){\n                try {\n                    const match = await this.createMatch(userId, potentialMatch.id);\n                    matches.push(match);\n                } catch (error) {\n                    console.error(`Error creating match with user ${potentialMatch.id}:`, error);\n                // Continue with other matches even if one fails\n                }\n            }\n            return matches;\n        } catch (error) {\n            console.error('Error generating daily matches:', error);\n            throw error;\n        }\n    }\n    static async generateSingleMatch(userId) {\n        try {\n            // 检查24小时内的匹配限制\n            const limitCheck = await this.checkDailyMatchLimit(userId);\n            if (!limitCheck.canMatch) {\n                const error = new Error('DAILY_LIMIT_EXCEEDED');\n                error.limitInfo = limitCheck;\n                throw error;\n            }\n            // Find one potential match for the user\n            const potentialMatches = await this.findPotentialMatches(userId, 1);\n            if (potentialMatches.length === 0) {\n                return null; // No more potential matches\n            }\n            const match = await this.createMatch(userId, potentialMatches[0].id);\n            return match;\n        } catch (error) {\n            console.error('Error generating single match:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/matching.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/drizzle-orm","vendor-chunks/postgres","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&page=%2Fapi%2Fmatches%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();