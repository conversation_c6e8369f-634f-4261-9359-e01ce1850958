/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/match/[id]/page";
exports.ids = ["app/match/[id]/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmatch%2F%5Bid%5D%2Fpage&page=%2Fmatch%2F%5Bid%5D%2Fpage&appPaths=%2Fmatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fmatch%2F%5Bid%5D%2Fpage.tsx&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmatch%2F%5Bid%5D%2Fpage&page=%2Fmatch%2F%5Bid%5D%2Fpage&appPaths=%2Fmatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fmatch%2F%5Bid%5D%2Fpage.tsx&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/match/[id]/page.tsx */ \"(rsc)/./src/app/match/[id]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'match',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/match/[id]/page\",\n        pathname: \"/match/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmatch%2F%5Bid%5D%2Fpage&page=%2Fmatch%2F%5Bid%5D%2Fpage&appPaths=%2Fmatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fmatch%2F%5Bid%5D%2Fpage.tsx&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/match/[id]/page.tsx */ \"(rsc)/./src/app/match/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZ1YnQyMiUyRndvcmtzcGFjZSUyRmluZGllJTJGbGluZ3hpYWktZ2VtaW5pJTJGc3JjJTJGYXBwJTJGbWF0Y2glMkYlNUJpZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3VidDIyL3dvcmtzcGFjZS9pbmRpZS9saW5neGlhaS1nZW1pbmkvc3JjL2FwcC9tYXRjaC9baWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: '灵犀AI - 智能恋爱匹配平台',\n    description: '基于AI技术的智能恋爱匹配平台，通过深度分析为您找到最合适的伴侣'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ+eBteeKgEFJIC0g5pm66IO95oGL54ix5Yy56YWN5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5BSeaKgOacr+eahOaZuuiDveaBi+eIseWMuemFjeW5s+WPsO+8jOmAmui/h+a3seW6puWIhuaekOS4uuaCqOaJvuWIsOacgOWQiOmAgueahOS8tOS+oycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/match/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/match/[id]/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/match/[id]/page.tsx */ \"(ssr)/./src/app/match/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZ1YnQyMiUyRndvcmtzcGFjZSUyRmluZGllJTJGbGluZ3hpYWktZ2VtaW5pJTJGc3JjJTJGYXBwJTJGbWF0Y2glMkYlNUJpZCU1RCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNEciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL3VidDIyL3dvcmtzcGFjZS9pbmRpZS9saW5neGlhaS1nZW1pbmkvc3JjL2FwcC9tYXRjaC9baWRdL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp%2Fmatch%2F%5Bid%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/match/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/match/[id]/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MatchDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction MatchDetailPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [match, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentMessageIndex, setCurrentMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAllMessages, setShowAllMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRating, setSelectedRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedbackSubmitted, setFeedbackSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模拟数据 - 在实际应用中这里会从API获取\n    const mockMatch = {\n        id: '1',\n        compatibilityScore: 85,\n        otherUser: {\n            name: '小雨',\n            age: 26,\n            location: '北京',\n            bio: '喜欢阅读和旅行，寻找有趣的灵魂',\n            interests: [\n                '阅读',\n                '旅行',\n                '摄影',\n                '咖啡',\n                '电影',\n                '音乐',\n                '瑜伽'\n            ],\n            avatar: '🌸'\n        },\n        aiAnalysis: {\n            explanation: '你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补，你们可能会在艺术、文化和人生哲学方面有很多共同话题。建议从共同的兴趣话题开始交流，比如最近读过的书或者想去的旅行目的地。',\n            strengths: [\n                '价值观高度一致',\n                '兴趣爱好互补',\n                '都热爱学习成长',\n                '沟通风格匹配'\n            ],\n            challenges: [\n                '生活节奏可能不同',\n                '需要平衡独处与社交时间'\n            ],\n            suggestions: [\n                '从共同兴趣开始聊天',\n                '分享彼此的读书心得',\n                '计划一次文化之旅'\n            ]\n        },\n        conversationSimulation: {\n            conversation: [\n                {\n                    speaker: 'user1',\n                    message: '你好！看到你也喜欢旅行，最近去过哪里？'\n                },\n                {\n                    speaker: 'user2',\n                    message: '你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。'\n                },\n                {\n                    speaker: 'user1',\n                    message: '是的！我特别想去布达拉宫，还想尝试一下藏式瑜伽，听说在高原上练瑜伽是完全不同的体验。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '这个想法太棒了！我也练瑜伽，在大自然中练习确实会有不一样的感受。我们可以一起规划路线吗？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '当然可以！我已经收集了一些攻略，我们可以分享一下彼此的想法。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '太好了！我觉得我们会有很多共同话题，期待更深入的交流。'\n                }\n            ],\n            analysis: {\n                conversationFlow: 92,\n                valueAlignment: 88,\n                communicationMatch: 90,\n                overallCompatibility: 85,\n                commonTopics: [\n                    '旅行',\n                    '瑜伽',\n                    '文化探索',\n                    '自然风光'\n                ],\n                potentialConflicts: [\n                    '时间安排差异',\n                    '旅行预算考虑'\n                ]\n            }\n        },\n        status: 'pending'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchDetailPage.useEffect\": ()=>{\n            loadMatchData();\n        }\n    }[\"MatchDetailPage.useEffect\"], []);\n    const loadMatchData = async ()=>{\n        try {\n            const response = await fetch(`/api/matches/${params.id}`);\n            if (response.ok) {\n                const data = await response.json();\n                setMatch(data.match);\n            } else {\n                console.error('Failed to load match data');\n                // 如果API失败，使用模拟数据作为后备\n                setMatch(mockMatch);\n            }\n        } catch (error) {\n            console.error('Error loading match data:', error);\n            // 如果出错，使用模拟数据作为后备\n            setMatch(mockMatch);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const startConversationAnimation = ()=>{\n        if (!match?.conversationSimulation?.conversation) return;\n        setIsPlaying(true);\n        setCurrentMessageIndex(0);\n        setShowAllMessages(false);\n        intervalRef.current = setInterval(()=>{\n            setCurrentMessageIndex((prev)=>{\n                const nextIndex = prev + 1;\n                if (nextIndex >= match.conversationSimulation.conversation.length) {\n                    setIsPlaying(false);\n                    setShowAllMessages(true);\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                    return prev;\n                }\n                return nextIndex;\n            });\n        }, 2000); // 每2秒显示一条消息\n    };\n    const pauseConversationAnimation = ()=>{\n        setIsPlaying(false);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n    };\n    const showAllConversation = ()=>{\n        setIsPlaying(false);\n        setShowAllMessages(true);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        setCurrentMessageIndex(match?.conversationSimulation?.conversation?.length || 0);\n    };\n    const handleFeedback = async (type, rating)=>{\n        // 这里会调用API保存反馈\n        console.log('Feedback:', {\n            type,\n            rating: rating || selectedRating\n        });\n        setFeedbackSubmitted(true);\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreLabel = (score)=>{\n        if (score >= 80) return '高度匹配';\n        if (score >= 60) return '中等匹配';\n        return '低度匹配';\n    };\n    const handleLike = async ()=>{\n        if (!match?.id) return;\n        try {\n            console.log('Sending like request for match:', match.id);\n            const response = await fetch(`/api/matches/${match.id}`, {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: true\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully liked match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to like match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error liking match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    const handlePass = async ()=>{\n        if (!match?.id) return;\n        try {\n            console.log('Sending pass request for match:', match.id);\n            const response = await fetch(`/api/matches/${match.id}`, {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: false\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully passed match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to pass match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error passing match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"加载匹配详情...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    if (!match) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"未找到匹配信息\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    const conversation = match.conversationSimulation?.conversation || [];\n    const analysis = match.conversationSimulation?.analysis || {};\n    const visibleMessages = showAllMessages ? conversation : conversation.slice(0, currentMessageIndex + 1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                href: \"/dashboard\",\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"匹配详情\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl\",\n                                                    children: match.otherUser.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: match.otherUser.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-pink-100\",\n                                                            children: [\n                                                                match.otherUser.age,\n                                                                \"岁 \\xb7 \",\n                                                                match.otherUser.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: [\n                                                        match.compatibilityScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-pink-100\",\n                                                    children: getScoreLabel(match.compatibilityScore)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-4\",\n                                        children: match.otherUser.bio\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"兴趣爱好\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: match.otherUser.interests.map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-sm\",\n                                                        children: interest\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"跳过\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"lg\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"喜欢\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"AI 深度分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"基于双方资料的智能匹配分析\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"匹配解析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n                                                    components: {\n                                                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-lg font-bold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-base font-semibold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium mb-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-3 leading-relaxed\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 42\n                                                            }, void 0),\n                                                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"list-disc list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 43\n                                                            }, void 0),\n                                                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                className: \"font-semibold text-gray-800\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 47\n                                                            }, void 0),\n                                                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                className: \"italic text-gray-600\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 43\n                                                            }, void 0)\n                                                    },\n                                                    children: match.aiAnalysis?.explanation || '暂无详细分析'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-green-600\",\n                                                        children: \"匹配优势\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: match.aiAnalysis?.strengths?.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    strength\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this)) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-yellow-600\",\n                                                        children: \"注意事项\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: match.aiAnalysis?.challenges?.map((challenge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    challenge\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this)) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"建议话题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: match.aiAnalysis?.suggestions?.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-blue-600 border-blue-200\",\n                                                        children: suggestion\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"暂无建议话题\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    match.aiAnalysis?.datePlan && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDC95\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"专属约会计划\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"AI 为你们量身定制的约会建议\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2 text-pink-600\",\n                                                children: \"约会主题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700\",\n                                                children: match.aiAnalysis.datePlan.theme\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: match.aiAnalysis.datePlan.concept\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"约会安排\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: match.aiAnalysis.datePlan.timeline?.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-blue-600 min-w-[80px]\",\n                                                                children: item.time\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.activity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: item.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: item.reason\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-green-600\",\n                                                        children: \"推荐地点\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: match.aiAnalysis.datePlan.recommendations?.locations?.map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    location\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-purple-600\",\n                                                        children: \"贴心提醒\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: match.aiAnalysis.datePlan.recommendations?.tips?.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    tip\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"预算建议：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.budget\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"约会时长：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\uD83D\\uDCAC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"AI 对话模拟\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"预测你们第一次聊天的场景\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                !isPlaying && currentMessageIndex === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: startConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"播放对话\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: pauseConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"暂停\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMessageIndex > 0 && !showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: showAllConversation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"显示全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto\",\n                                        children: [\n                                            visibleMessages.map((message, index)=>{\n                                                // 判断是否是当前用户的消息（假设第一个说话的是对方用户）\n                                                const isCurrentUser = index % 2 === 1; // 简单的交替逻辑，可以根据实际需求调整\n                                                const isOtherUser = message.speaker === match.otherUser?.name;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `flex ${isOtherUser ? 'justify-start' : 'justify-end'} ${index === currentMessageIndex && isPlaying ? 'animate-pulse' : ''}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `max-w-[70%] p-3 rounded-lg transition-all duration-500 ${isOtherUser ? 'bg-white border shadow-sm text-gray-800' : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'} ${index <= currentMessageIndex ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-xs mb-1 ${isOtherUser ? 'text-gray-500' : 'text-blue-100'}`,\n                                                                children: message.speaker\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            isPlaying && currentMessageIndex < conversation.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"对话分析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"对话流畅度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-sm font-medium ${getScoreColor(analysis.conversationFlow || 0)}`,\n                                                                        children: [\n                                                                            analysis.conversationFlow || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"价值观契合\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-sm font-medium ${getScoreColor(analysis.valueAlignment || 0)}`,\n                                                                        children: [\n                                                                            analysis.valueAlignment || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"沟通匹配度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-sm font-medium ${getScoreColor(analysis.communicationMatch || 0)}`,\n                                                                        children: [\n                                                                            analysis.communicationMatch || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"整体兼容性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `text-sm font-medium ${getScoreColor(analysis.overallCompatibility || 0)}`,\n                                                                        children: [\n                                                                            analysis.overallCompatibility || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            analysis?.commonTopics && analysis.commonTopics.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-sm font-medium mb-2\",\n                                                        children: \"共同话题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: analysis.commonTopics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs\",\n                                                                children: topic\n                                                            }, index, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"这个对话模拟准确吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this),\n                                            !feedbackSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"准确度评分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5\n                                                                ].map((rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedRating(rating),\n                                                                        className: `p-1 transition-colors ${selectedRating && selectedRating >= rating ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-400'}`,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4 fill-current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, rating, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('accurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"很准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('inaccurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"不太准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600 text-sm bg-green-50 p-3 rounded-md\",\n                                                children: \"✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/match/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL2hvbWUvdWJ0MjIvd29ya3NwYWNlL2luZGllL2xpbmd4aWFpLWdlbWluaS9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/lucide-react","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/micromark","vendor-chunks/micromark-util-symbol","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/style-to-js","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-from-markdown","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmatch%2F%5Bid%5D%2Fpage&page=%2Fmatch%2F%5Bid%5D%2Fpage&appPaths=%2Fmatch%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fmatch%2F%5Bid%5D%2Fpage.tsx&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();