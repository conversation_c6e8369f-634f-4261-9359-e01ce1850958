"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/match/[id]/page",{

/***/ "(app-pages-browser)/./src/app/match/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/match/[id]/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MatchDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MatchDetailPage() {\n    var _match_conversationSimulation, _match_conversationSimulation1, _match_aiAnalysis, _match_aiAnalysis_strengths, _match_aiAnalysis1, _match_aiAnalysis_challenges, _match_aiAnalysis2, _match_aiAnalysis_suggestions, _match_aiAnalysis3, _match_aiAnalysis4, _match_aiAnalysis_datePlan_timeline, _match_aiAnalysis_datePlan_recommendations_locations, _match_aiAnalysis_datePlan_recommendations, _match_aiAnalysis_datePlan_recommendations_tips, _match_aiAnalysis_datePlan_recommendations1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [match, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentMessageIndex, setCurrentMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAllMessages, setShowAllMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRating, setSelectedRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedbackSubmitted, setFeedbackSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模拟数据 - 在实际应用中这里会从API获取\n    const mockMatch = {\n        id: '1',\n        compatibilityScore: 85,\n        otherUser: {\n            name: '小雨',\n            age: 26,\n            location: '北京',\n            bio: '喜欢阅读和旅行，寻找有趣的灵魂',\n            interests: [\n                '阅读',\n                '旅行',\n                '摄影',\n                '咖啡',\n                '电影',\n                '音乐',\n                '瑜伽'\n            ],\n            avatar: '🌸'\n        },\n        aiAnalysis: {\n            explanation: '你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补，你们可能会在艺术、文化和人生哲学方面有很多共同话题。建议从共同的兴趣话题开始交流，比如最近读过的书或者想去的旅行目的地。',\n            strengths: [\n                '价值观高度一致',\n                '兴趣爱好互补',\n                '都热爱学习成长',\n                '沟通风格匹配'\n            ],\n            challenges: [\n                '生活节奏可能不同',\n                '需要平衡独处与社交时间'\n            ],\n            suggestions: [\n                '从共同兴趣开始聊天',\n                '分享彼此的读书心得',\n                '计划一次文化之旅'\n            ]\n        },\n        conversationSimulation: {\n            conversation: [\n                {\n                    speaker: 'user1',\n                    message: '你好！看到你也喜欢旅行，最近去过哪里？'\n                },\n                {\n                    speaker: 'user2',\n                    message: '你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。'\n                },\n                {\n                    speaker: 'user1',\n                    message: '是的！我特别想去布达拉宫，还想尝试一下藏式瑜伽，听说在高原上练瑜伽是完全不同的体验。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '这个想法太棒了！我也练瑜伽，在大自然中练习确实会有不一样的感受。我们可以一起规划路线吗？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '当然可以！我已经收集了一些攻略，我们可以分享一下彼此的想法。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '太好了！我觉得我们会有很多共同话题，期待更深入的交流。'\n                }\n            ],\n            analysis: {\n                conversationFlow: 92,\n                valueAlignment: 88,\n                communicationMatch: 90,\n                overallCompatibility: 85,\n                commonTopics: [\n                    '旅行',\n                    '瑜伽',\n                    '文化探索',\n                    '自然风光'\n                ],\n                potentialConflicts: [\n                    '时间安排差异',\n                    '旅行预算考虑'\n                ]\n            }\n        },\n        status: 'pending'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchDetailPage.useEffect\": ()=>{\n            loadMatchData();\n        }\n    }[\"MatchDetailPage.useEffect\"], []);\n    const loadMatchData = async ()=>{\n        try {\n            console.log('Loading match data for ID:', params.id);\n            console.log('Full params object:', params);\n            const response = await fetch(\"/api/matches/\".concat(params.id));\n            if (response.ok) {\n                const data = await response.json();\n                setMatch(data.match);\n            } else {\n                console.error('Failed to load match data');\n                // 如果API失败，使用模拟数据作为后备\n                setMatch(mockMatch);\n            }\n        } catch (error) {\n            console.error('Error loading match data:', error);\n            // 如果出错，使用模拟数据作为后备\n            setMatch(mockMatch);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const startConversationAnimation = ()=>{\n        var _match_conversationSimulation;\n        if (!(match === null || match === void 0 ? void 0 : (_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : _match_conversationSimulation.conversation)) return;\n        setIsPlaying(true);\n        setCurrentMessageIndex(0);\n        setShowAllMessages(false);\n        intervalRef.current = setInterval(()=>{\n            setCurrentMessageIndex((prev)=>{\n                const nextIndex = prev + 1;\n                if (nextIndex >= match.conversationSimulation.conversation.length) {\n                    setIsPlaying(false);\n                    setShowAllMessages(true);\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                    return prev;\n                }\n                return nextIndex;\n            });\n        }, 2000); // 每2秒显示一条消息\n    };\n    const pauseConversationAnimation = ()=>{\n        setIsPlaying(false);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n    };\n    const showAllConversation = ()=>{\n        var _match_conversationSimulation_conversation, _match_conversationSimulation;\n        setIsPlaying(false);\n        setShowAllMessages(true);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        setCurrentMessageIndex((match === null || match === void 0 ? void 0 : (_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : (_match_conversationSimulation_conversation = _match_conversationSimulation.conversation) === null || _match_conversationSimulation_conversation === void 0 ? void 0 : _match_conversationSimulation_conversation.length) || 0);\n    };\n    const handleFeedback = async (type, rating)=>{\n        // 这里会调用API保存反馈\n        console.log('Feedback:', {\n            type,\n            rating: rating || selectedRating\n        });\n        setFeedbackSubmitted(true);\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreLabel = (score)=>{\n        if (score >= 80) return '高度匹配';\n        if (score >= 60) return '中等匹配';\n        return '低度匹配';\n    };\n    const handleLike = async ()=>{\n        if (!(match === null || match === void 0 ? void 0 : match.id)) {\n            console.error('No match ID available');\n            alert('匹配ID不存在，请刷新页面重试');\n            return;\n        }\n        try {\n            console.log('Sending like request for match:', match.id);\n            console.log('Match object:', match);\n            const response = await fetch(\"/api/matches/\".concat(match.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: true\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully liked match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to like match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error liking match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    const handlePass = async ()=>{\n        if (!(match === null || match === void 0 ? void 0 : match.id)) return;\n        try {\n            console.log('Sending pass request for match:', match.id);\n            const response = await fetch(\"/api/matches/\".concat(match.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: false\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully passed match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to pass match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error passing match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"加载匹配详情...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    if (!match) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"未找到匹配信息\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 234,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 233,\n            columnNumber: 7\n        }, this);\n    }\n    const conversation = ((_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : _match_conversationSimulation.conversation) || [];\n    const analysis = ((_match_conversationSimulation1 = match.conversationSimulation) === null || _match_conversationSimulation1 === void 0 ? void 0 : _match_conversationSimulation1.analysis) || {};\n    const visibleMessages = showAllMessages ? conversation : conversation.slice(0, currentMessageIndex + 1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                href: \"/dashboard\",\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"匹配详情\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl\",\n                                                    children: match.otherUser.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: match.otherUser.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-pink-100\",\n                                                            children: [\n                                                                match.otherUser.age,\n                                                                \"岁 \\xb7 \",\n                                                                match.otherUser.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: [\n                                                        match.compatibilityScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-pink-100\",\n                                                    children: getScoreLabel(match.compatibilityScore)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-4\",\n                                        children: match.otherUser.bio\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"兴趣爱好\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: match.otherUser.interests.map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-sm\",\n                                                        children: interest\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    match.status === 'pending' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                onClick: handlePass,\n                                                className: \"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"跳过\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"lg\",\n                                                onClick: handleLike,\n                                                className: \"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"喜欢\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    match.status === 'mutual_like' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-pink-50 to-red-50 border border-pink-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-2 text-pink-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 fill-current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"你们互相喜欢！\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-pink-600 mt-2\",\n                                                children: \"可以开始联系对方了 \\uD83D\\uDC95\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    match.status === 'passed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center gap-2 text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"已跳过此匹配\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this),\n                                    match.status === 'liked' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-2 text-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"你已表示喜欢\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-blue-600 mt-2\",\n                                                children: \"等待对方回应...\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"AI 深度分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"基于双方资料的智能匹配分析\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"匹配解析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n                                                    components: {\n                                                        h1: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-lg font-bold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        h2: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-base font-semibold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        h3: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium mb-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        p: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-3 leading-relaxed\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 42\n                                                            }, void 0);\n                                                        },\n                                                        ul: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"list-disc list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        ol: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        li: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        strong: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                className: \"font-semibold text-gray-800\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 47\n                                                            }, void 0);\n                                                        },\n                                                        em: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                className: \"italic text-gray-600\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        }\n                                                    },\n                                                    children: ((_match_aiAnalysis = match.aiAnalysis) === null || _match_aiAnalysis === void 0 ? void 0 : _match_aiAnalysis.explanation) || '暂无详细分析'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-green-600\",\n                                                        children: \"匹配优势\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: ((_match_aiAnalysis1 = match.aiAnalysis) === null || _match_aiAnalysis1 === void 0 ? void 0 : (_match_aiAnalysis_strengths = _match_aiAnalysis1.strengths) === null || _match_aiAnalysis_strengths === void 0 ? void 0 : _match_aiAnalysis_strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    strength\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 21\n                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-yellow-600\",\n                                                        children: \"注意事项\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: ((_match_aiAnalysis2 = match.aiAnalysis) === null || _match_aiAnalysis2 === void 0 ? void 0 : (_match_aiAnalysis_challenges = _match_aiAnalysis2.challenges) === null || _match_aiAnalysis_challenges === void 0 ? void 0 : _match_aiAnalysis_challenges.map((challenge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    challenge\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 21\n                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"建议话题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: ((_match_aiAnalysis3 = match.aiAnalysis) === null || _match_aiAnalysis3 === void 0 ? void 0 : (_match_aiAnalysis_suggestions = _match_aiAnalysis3.suggestions) === null || _match_aiAnalysis_suggestions === void 0 ? void 0 : _match_aiAnalysis_suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-blue-600 border-blue-200\",\n                                                        children: suggestion\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"暂无建议话题\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    ((_match_aiAnalysis4 = match.aiAnalysis) === null || _match_aiAnalysis4 === void 0 ? void 0 : _match_aiAnalysis4.datePlan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDC95\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"专属约会计划\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"AI 为你们量身定制的约会建议\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2 text-pink-600\",\n                                                children: \"约会主题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700\",\n                                                children: match.aiAnalysis.datePlan.theme\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: match.aiAnalysis.datePlan.concept\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"约会安排\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: (_match_aiAnalysis_datePlan_timeline = match.aiAnalysis.datePlan.timeline) === null || _match_aiAnalysis_datePlan_timeline === void 0 ? void 0 : _match_aiAnalysis_datePlan_timeline.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-blue-600 min-w-[80px]\",\n                                                                children: item.time\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.activity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: item.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: item.reason\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-green-600\",\n                                                        children: \"推荐地点\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: (_match_aiAnalysis_datePlan_recommendations = match.aiAnalysis.datePlan.recommendations) === null || _match_aiAnalysis_datePlan_recommendations === void 0 ? void 0 : (_match_aiAnalysis_datePlan_recommendations_locations = _match_aiAnalysis_datePlan_recommendations.locations) === null || _match_aiAnalysis_datePlan_recommendations_locations === void 0 ? void 0 : _match_aiAnalysis_datePlan_recommendations_locations.map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    location\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-purple-600\",\n                                                        children: \"贴心提醒\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: (_match_aiAnalysis_datePlan_recommendations1 = match.aiAnalysis.datePlan.recommendations) === null || _match_aiAnalysis_datePlan_recommendations1 === void 0 ? void 0 : (_match_aiAnalysis_datePlan_recommendations_tips = _match_aiAnalysis_datePlan_recommendations1.tips) === null || _match_aiAnalysis_datePlan_recommendations_tips === void 0 ? void 0 : _match_aiAnalysis_datePlan_recommendations_tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    tip\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"预算建议：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.budget\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"约会时长：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\uD83D\\uDCAC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"AI 对话模拟\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"预测你们第一次聊天的场景\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                !isPlaying && currentMessageIndex === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: startConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"播放对话\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: pauseConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"暂停\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMessageIndex > 0 && !showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: showAllConversation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"显示全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto\",\n                                        children: [\n                                            visibleMessages.map((message, index)=>{\n                                                var _match_otherUser;\n                                                // 判断是否是当前用户的消息（假设第一个说话的是对方用户）\n                                                const isCurrentUser = index % 2 === 1; // 简单的交替逻辑，可以根据实际需求调整\n                                                const isOtherUser = message.speaker === ((_match_otherUser = match.otherUser) === null || _match_otherUser === void 0 ? void 0 : _match_otherUser.name);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(isOtherUser ? 'justify-start' : 'justify-end', \" \").concat(index === currentMessageIndex && isPlaying ? 'animate-pulse' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-[70%] p-3 rounded-lg transition-all duration-500 \".concat(isOtherUser ? 'bg-white border shadow-sm text-gray-800' : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white', \" \").concat(index <= currentMessageIndex ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs mb-1 \".concat(isOtherUser ? 'text-gray-500' : 'text-blue-100'),\n                                                                children: message.speaker\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            isPlaying && currentMessageIndex < conversation.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"对话分析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"对话流畅度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.conversationFlow || 0)),\n                                                                        children: [\n                                                                            analysis.conversationFlow || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"价值观契合\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.valueAlignment || 0)),\n                                                                        children: [\n                                                                            analysis.valueAlignment || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 609,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"沟通匹配度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 617,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.communicationMatch || 0)),\n                                                                        children: [\n                                                                            analysis.communicationMatch || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"整体兼容性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.overallCompatibility || 0)),\n                                                                        children: [\n                                                                            analysis.overallCompatibility || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this),\n                                            (analysis === null || analysis === void 0 ? void 0 : analysis.commonTopics) && analysis.commonTopics.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-sm font-medium mb-2\",\n                                                        children: \"共同话题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: analysis.commonTopics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs\",\n                                                                children: topic\n                                                            }, index, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 15\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"这个对话模拟准确吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            !feedbackSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"准确度评分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5\n                                                                ].map((rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedRating(rating),\n                                                                        className: \"p-1 transition-colors \".concat(selectedRating && selectedRating >= rating ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-400'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4 fill-current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                            lineNumber: 666,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, rating, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 657,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('accurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"很准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('inaccurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"不太准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600 text-sm bg-green-50 p-3 rounded-md\",\n                                                children: \"✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\n_s(MatchDetailPage, \"OarUNys1KVMuJJBx0nODQvqbkTA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = MatchDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MatchDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/match/[id]/page.tsx\n"));

/***/ })

});