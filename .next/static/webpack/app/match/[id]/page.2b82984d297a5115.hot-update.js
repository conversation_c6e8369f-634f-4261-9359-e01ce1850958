"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/match/[id]/page",{

/***/ "(app-pages-browser)/./src/app/match/[id]/page.tsx":
/*!*************************************!*\
  !*** ./src/app/match/[id]/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MatchDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Heart,Pause,Play,Star,ThumbsDown,ThumbsUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MatchDetailPage() {\n    var _match_conversationSimulation, _match_conversationSimulation1, _match_aiAnalysis, _match_aiAnalysis_strengths, _match_aiAnalysis1, _match_aiAnalysis_challenges, _match_aiAnalysis2, _match_aiAnalysis_suggestions, _match_aiAnalysis3, _match_aiAnalysis4, _match_aiAnalysis_datePlan_timeline, _match_aiAnalysis_datePlan_recommendations_locations, _match_aiAnalysis_datePlan_recommendations, _match_aiAnalysis_datePlan_recommendations_tips, _match_aiAnalysis_datePlan_recommendations1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [match, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentMessageIndex, setCurrentMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAllMessages, setShowAllMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRating, setSelectedRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedbackSubmitted, setFeedbackSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 模拟数据 - 在实际应用中这里会从API获取\n    const mockMatch = {\n        id: '1',\n        compatibilityScore: 85,\n        otherUser: {\n            name: '小雨',\n            age: 26,\n            location: '北京',\n            bio: '喜欢阅读和旅行，寻找有趣的灵魂',\n            interests: [\n                '阅读',\n                '旅行',\n                '摄影',\n                '咖啡',\n                '电影',\n                '音乐',\n                '瑜伽'\n            ],\n            avatar: '🌸'\n        },\n        aiAnalysis: {\n            explanation: '你们在价值观和兴趣爱好方面有很高的契合度，都喜欢深度思考和探索世界。小雨的文艺气质与你的理性思维形成很好的互补，你们可能会在艺术、文化和人生哲学方面有很多共同话题。建议从共同的兴趣话题开始交流，比如最近读过的书或者想去的旅行目的地。',\n            strengths: [\n                '价值观高度一致',\n                '兴趣爱好互补',\n                '都热爱学习成长',\n                '沟通风格匹配'\n            ],\n            challenges: [\n                '生活节奏可能不同',\n                '需要平衡独处与社交时间'\n            ],\n            suggestions: [\n                '从共同兴趣开始聊天',\n                '分享彼此的读书心得',\n                '计划一次文化之旅'\n            ]\n        },\n        conversationSimulation: {\n            conversation: [\n                {\n                    speaker: 'user1',\n                    message: '你好！看到你也喜欢旅行，最近去过哪里？'\n                },\n                {\n                    speaker: 'user2',\n                    message: '你好！刚从云南回来，那里的风景真的很美，特别是洱海的日出。你呢？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '云南确实不错！我最近在计划去西藏，一直想体验那里的文化和星空。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '哇，西藏！我也一直想去，听说那里的星空特别美，还有很多古老的寺庙。'\n                },\n                {\n                    speaker: 'user1',\n                    message: '是的！我特别想去布达拉宫，还想尝试一下藏式瑜伽，听说在高原上练瑜伽是完全不同的体验。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '这个想法太棒了！我也练瑜伽，在大自然中练习确实会有不一样的感受。我们可以一起规划路线吗？'\n                },\n                {\n                    speaker: 'user1',\n                    message: '当然可以！我已经收集了一些攻略，我们可以分享一下彼此的想法。'\n                },\n                {\n                    speaker: 'user2',\n                    message: '太好了！我觉得我们会有很多共同话题，期待更深入的交流。'\n                }\n            ],\n            analysis: {\n                conversationFlow: 92,\n                valueAlignment: 88,\n                communicationMatch: 90,\n                overallCompatibility: 85,\n                commonTopics: [\n                    '旅行',\n                    '瑜伽',\n                    '文化探索',\n                    '自然风光'\n                ],\n                potentialConflicts: [\n                    '时间安排差异',\n                    '旅行预算考虑'\n                ]\n            }\n        },\n        status: 'pending'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MatchDetailPage.useEffect\": ()=>{\n            loadMatchData();\n        }\n    }[\"MatchDetailPage.useEffect\"], []);\n    const loadMatchData = async ()=>{\n        try {\n            const response = await fetch(\"/api/matches/\".concat(params.id));\n            if (response.ok) {\n                const data = await response.json();\n                setMatch(data.match);\n            } else {\n                console.error('Failed to load match data');\n                // 如果API失败，使用模拟数据作为后备\n                setMatch(mockMatch);\n            }\n        } catch (error) {\n            console.error('Error loading match data:', error);\n            // 如果出错，使用模拟数据作为后备\n            setMatch(mockMatch);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const startConversationAnimation = ()=>{\n        var _match_conversationSimulation;\n        if (!(match === null || match === void 0 ? void 0 : (_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : _match_conversationSimulation.conversation)) return;\n        setIsPlaying(true);\n        setCurrentMessageIndex(0);\n        setShowAllMessages(false);\n        intervalRef.current = setInterval(()=>{\n            setCurrentMessageIndex((prev)=>{\n                const nextIndex = prev + 1;\n                if (nextIndex >= match.conversationSimulation.conversation.length) {\n                    setIsPlaying(false);\n                    setShowAllMessages(true);\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                    return prev;\n                }\n                return nextIndex;\n            });\n        }, 2000); // 每2秒显示一条消息\n    };\n    const pauseConversationAnimation = ()=>{\n        setIsPlaying(false);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n    };\n    const showAllConversation = ()=>{\n        var _match_conversationSimulation_conversation, _match_conversationSimulation;\n        setIsPlaying(false);\n        setShowAllMessages(true);\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n        }\n        setCurrentMessageIndex((match === null || match === void 0 ? void 0 : (_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : (_match_conversationSimulation_conversation = _match_conversationSimulation.conversation) === null || _match_conversationSimulation_conversation === void 0 ? void 0 : _match_conversationSimulation_conversation.length) || 0);\n    };\n    const handleFeedback = async (type, rating)=>{\n        // 这里会调用API保存反馈\n        console.log('Feedback:', {\n            type,\n            rating: rating || selectedRating\n        });\n        setFeedbackSubmitted(true);\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreLabel = (score)=>{\n        if (score >= 80) return '高度匹配';\n        if (score >= 60) return '中等匹配';\n        return '低度匹配';\n    };\n    const handleLike = async ()=>{\n        if (!(match === null || match === void 0 ? void 0 : match.id)) return;\n        try {\n            console.log('Sending like request for match:', match.id);\n            const response = await fetch(\"/api/matches/\".concat(match.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: true\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully liked match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to like match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error liking match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    const handlePass = async ()=>{\n        if (!(match === null || match === void 0 ? void 0 : match.id)) return;\n        try {\n            console.log('Sending pass request for match:', match.id);\n            const response = await fetch(\"/api/matches/\".concat(match.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    liked: false\n                })\n            });\n            console.log('Response status:', response.status);\n            const responseData = await response.json();\n            console.log('Response data:', responseData);\n            if (response.ok) {\n                console.log('Successfully passed match:', match.id);\n                router.push('/dashboard');\n            } else {\n                console.error('Failed to pass match:', responseData);\n                alert('操作失败，请重试');\n            }\n        } catch (error) {\n            console.error('Error passing match:', error);\n            alert('网络错误，请重试');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"加载匹配详情...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this);\n    }\n    if (!match) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"未找到匹配信息\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    const conversation = ((_match_conversationSimulation = match.conversationSimulation) === null || _match_conversationSimulation === void 0 ? void 0 : _match_conversationSimulation.conversation) || [];\n    const analysis = ((_match_conversationSimulation1 = match.conversationSimulation) === null || _match_conversationSimulation1 === void 0 ? void 0 : _match_conversationSimulation1.analysis) || {};\n    const visibleMessages = showAllMessages ? conversation : conversation.slice(0, currentMessageIndex + 1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-pink-50 via-white to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                href: \"/dashboard\",\n                                className: \"flex items-center gap-2 text-gray-600 hover:text-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"返回\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"匹配详情\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-r from-pink-500 to-blue-500 p-6 text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-2xl\",\n                                                    children: match.otherUser.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold\",\n                                                            children: match.otherUser.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-pink-100\",\n                                                            children: [\n                                                                match.otherUser.age,\n                                                                \"岁 \\xb7 \",\n                                                                match.otherUser.location\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: [\n                                                        match.compatibilityScore,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-pink-100\",\n                                                    children: getScoreLabel(match.compatibilityScore)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-4\",\n                                        children: match.otherUser.bio\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"兴趣爱好\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: match.otherUser.interests.map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-sm\",\n                                                        children: interest\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"跳过\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"lg\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-blue-600 hover:from-pink-700 hover:to-blue-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"喜欢\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"AI 深度分析\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"基于双方资料的智能匹配分析\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"匹配解析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"prose prose-sm max-w-none text-gray-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_10__.Markdown, {\n                                                    components: {\n                                                        h1: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-lg font-bold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        h2: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-base font-semibold mb-2\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        h3: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium mb-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        p: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-3 leading-relaxed\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 42\n                                                            }, void 0);\n                                                        },\n                                                        ul: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"list-disc list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        ol: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                className: \"list-decimal list-inside mb-3 space-y-1\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        li: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        },\n                                                        strong: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                className: \"font-semibold text-gray-800\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 47\n                                                            }, void 0);\n                                                        },\n                                                        em: (param)=>{\n                                                            let { children } = param;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                                                className: \"italic text-gray-600\",\n                                                                children: children\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 43\n                                                            }, void 0);\n                                                        }\n                                                    },\n                                                    children: ((_match_aiAnalysis = match.aiAnalysis) === null || _match_aiAnalysis === void 0 ? void 0 : _match_aiAnalysis.explanation) || '暂无详细分析'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-green-600\",\n                                                        children: \"匹配优势\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: ((_match_aiAnalysis1 = match.aiAnalysis) === null || _match_aiAnalysis1 === void 0 ? void 0 : (_match_aiAnalysis_strengths = _match_aiAnalysis1.strengths) === null || _match_aiAnalysis_strengths === void 0 ? void 0 : _match_aiAnalysis_strengths.map((strength, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    strength\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-3 text-yellow-600\",\n                                                        children: \"注意事项\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2\",\n                                                        children: ((_match_aiAnalysis2 = match.aiAnalysis) === null || _match_aiAnalysis2 === void 0 ? void 0 : (_match_aiAnalysis_challenges = _match_aiAnalysis2.challenges) === null || _match_aiAnalysis_challenges === void 0 ? void 0 : _match_aiAnalysis_challenges.map((challenge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-center gap-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    challenge\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 21\n                                                            }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"暂无数据\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"建议话题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: ((_match_aiAnalysis3 = match.aiAnalysis) === null || _match_aiAnalysis3 === void 0 ? void 0 : (_match_aiAnalysis_suggestions = _match_aiAnalysis3.suggestions) === null || _match_aiAnalysis_suggestions === void 0 ? void 0 : _match_aiAnalysis_suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-blue-600 border-blue-200\",\n                                                        children: suggestion\n                                                    }, index, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"暂无建议话题\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    ((_match_aiAnalysis4 = match.aiAnalysis) === null || _match_aiAnalysis4 === void 0 ? void 0 : _match_aiAnalysis4.datePlan) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDC95\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"专属约会计划\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"AI 为你们量身定制的约会建议\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2 text-pink-600\",\n                                                children: \"约会主题\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700\",\n                                                children: match.aiAnalysis.datePlan.theme\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mt-1\",\n                                                children: match.aiAnalysis.datePlan.concept\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3 text-blue-600\",\n                                                children: \"约会安排\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: (_match_aiAnalysis_datePlan_timeline = match.aiAnalysis.datePlan.timeline) === null || _match_aiAnalysis_datePlan_timeline === void 0 ? void 0 : _match_aiAnalysis_datePlan_timeline.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-blue-600 min-w-[80px]\",\n                                                                children: item.time\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: item.activity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: item.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                                        children: item.reason\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-green-600\",\n                                                        children: \"推荐地点\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: (_match_aiAnalysis_datePlan_recommendations = match.aiAnalysis.datePlan.recommendations) === null || _match_aiAnalysis_datePlan_recommendations === void 0 ? void 0 : (_match_aiAnalysis_datePlan_recommendations_locations = _match_aiAnalysis_datePlan_recommendations.locations) === null || _match_aiAnalysis_datePlan_recommendations_locations === void 0 ? void 0 : _match_aiAnalysis_datePlan_recommendations_locations.map((location, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-green-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    location\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2 text-purple-600\",\n                                                        children: \"贴心提醒\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-1\",\n                                                        children: (_match_aiAnalysis_datePlan_recommendations1 = match.aiAnalysis.datePlan.recommendations) === null || _match_aiAnalysis_datePlan_recommendations1 === void 0 ? void 0 : (_match_aiAnalysis_datePlan_recommendations_tips = _match_aiAnalysis_datePlan_recommendations1.tips) === null || _match_aiAnalysis_datePlan_recommendations_tips === void 0 ? void 0 : _match_aiAnalysis_datePlan_recommendations_tips.map((tip, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"text-sm text-gray-700 flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-1.5 h-1.5 bg-purple-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    tip\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"预算建议：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.budget\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"约会时长：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    match.aiAnalysis.datePlan.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: \"\\uD83D\\uDCAC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"AI 对话模拟\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    children: \"预测你们第一次聊天的场景\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                !isPlaying && currentMessageIndex === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: startConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"播放对话\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: pauseConversationAnimation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"暂停\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, this),\n                                                currentMessageIndex > 0 && !showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: showAllConversation,\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"显示全部\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg p-4 space-y-4 max-h-96 overflow-y-auto\",\n                                        children: [\n                                            visibleMessages.map((message, index)=>{\n                                                var _match_otherUser;\n                                                // 判断是否是当前用户的消息（假设第一个说话的是对方用户）\n                                                const isCurrentUser = index % 2 === 1; // 简单的交替逻辑，可以根据实际需求调整\n                                                const isOtherUser = message.speaker === ((_match_otherUser = match.otherUser) === null || _match_otherUser === void 0 ? void 0 : _match_otherUser.name);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(isOtherUser ? 'justify-start' : 'justify-end', \" \").concat(index === currentMessageIndex && isPlaying ? 'animate-pulse' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-[70%] p-3 rounded-lg transition-all duration-500 \".concat(isOtherUser ? 'bg-white border shadow-sm text-gray-800' : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white', \" \").concat(index <= currentMessageIndex ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs mb-1 \".concat(isOtherUser ? 'text-gray-500' : 'text-blue-100'),\n                                                                children: message.speaker\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm leading-relaxed\",\n                                                                children: message.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            isPlaying && currentMessageIndex < conversation.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.1s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                            style: {\n                                                                animationDelay: '0.2s'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium\",\n                                                children: \"对话分析\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"对话流畅度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.conversationFlow || 0)),\n                                                                        children: [\n                                                                            analysis.conversationFlow || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"价值观契合\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.valueAlignment || 0)),\n                                                                        children: [\n                                                                            analysis.valueAlignment || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"沟通匹配度\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.communicationMatch || 0)),\n                                                                        children: [\n                                                                            analysis.communicationMatch || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 568,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"整体兼容性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium \".concat(getScoreColor(analysis.overallCompatibility || 0)),\n                                                                        children: [\n                                                                            analysis.overallCompatibility || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 17\n                                            }, this),\n                                            (analysis === null || analysis === void 0 ? void 0 : analysis.commonTopics) && analysis.commonTopics.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-sm font-medium mb-2\",\n                                                        children: \"共同话题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: analysis.commonTopics.map((topic, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs\",\n                                                                children: topic\n                                                            }, index, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this),\n                                    showAllMessages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-3\",\n                                                children: \"这个对话模拟准确吗？\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this),\n                                            !feedbackSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"准确度评分：\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1\",\n                                                                children: [\n                                                                    1,\n                                                                    2,\n                                                                    3,\n                                                                    4,\n                                                                    5\n                                                                ].map((rating)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedRating(rating),\n                                                                        className: \"p-1 transition-colors \".concat(selectedRating && selectedRating >= rating ? 'text-yellow-500' : 'text-gray-300 hover:text-yellow-400'),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"w-4 h-4 fill-current\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                            lineNumber: 616,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, rating, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('accurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"很准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleFeedback('inaccurate'),\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Heart_Pause_Play_Star_ThumbsDown_ThumbsUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"不太准确\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600 text-sm bg-green-50 p-3 rounded-md\",\n                                                children: \"✨ 感谢您的反馈！这将帮助我们改进AI分析的准确性。\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/match/[id]/page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(MatchDetailPage, \"OarUNys1KVMuJJBx0nODQvqbkTA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = MatchDetailPage;\nvar _c;\n$RefreshReg$(_c, \"MatchDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/match/[id]/page.tsx\n"));

/***/ })

});