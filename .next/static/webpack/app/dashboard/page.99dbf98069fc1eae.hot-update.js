"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/MatchCard.tsx":
/*!**************************************!*\
  !*** ./src/components/MatchCard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchCard: () => (/* binding */ MatchCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,Sparkles,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ MatchCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MatchCard(param) {\n    let { match, onLike, onPass } = param;\n    var _otherUser_name, _match_aiAnalysis;\n    _s();\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const otherUser = match.otherUser || match.user2;\n    const compatibilityScore = match.compatibilityScore || 0;\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreLabel = (score)=>{\n        if (score >= 80) return '高度匹配';\n        if (score >= 60) return '中等匹配';\n        return '低度匹配';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full max-w-md mx-auto hover:shadow-xl hover:scale-105 transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-4 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-pink-100 to-blue-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-14 h-14 bg-gradient-to-br from-pink-500 to-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-lg\",\n                                        children: otherUser === null || otherUser === void 0 ? void 0 : (_otherUser_name = otherUser.name) === null || _otherUser_name === void 0 ? void 0 : _otherUser_name.charAt(0)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl font-bold text-gray-800\",\n                                                children: otherUser === null || otherUser === void 0 ? void 0 : otherUser.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                className: \"text-gray-600 font-medium\",\n                                                children: [\n                                                    otherUser === null || otherUser === void 0 ? void 0 : otherUser.age,\n                                                    \"岁 \\xb7 \",\n                                                    otherUser === null || otherUser === void 0 ? void 0 : otherUser.location\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold \".concat(getScoreColor(compatibilityScore), \" drop-shadow-sm\"),\n                                        children: [\n                                            compatibilityScore,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500 font-medium\",\n                                        children: getScoreLabel(compatibilityScore)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-5 relative\",\n                children: [\n                    (otherUser === null || otherUser === void 0 ? void 0 : otherUser.bio) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 rounded-lg p-4 border-l-4 border-gradient-to-b from-pink-400 to-blue-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 leading-relaxed italic\",\n                            children: [\n                                '\"',\n                                otherUser.bio,\n                                '\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    (otherUser === null || otherUser === void 0 ? void 0 : otherUser.interests) && otherUser.interests.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold mb-3 text-gray-800 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"兴趣爱好\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    otherUser.interests.slice(0, 5).map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            className: \"text-xs bg-gradient-to-r from-pink-100 to-blue-100 text-gray-700 border-0 hover:from-pink-200 hover:to-blue-200 transition-colors\",\n                                            children: interest\n                                        }, index, false, {\n                                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)),\n                                    otherUser.interests.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"outline\",\n                                        className: \"text-xs border-gray-300 text-gray-600\",\n                                        children: [\n                                            \"+\",\n                                            otherUser.interests.length - 5\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    ((_match_aiAnalysis = match.aiAnalysis) === null || _match_aiAnalysis === void 0 ? void 0 : _match_aiAnalysis.explanation) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83E\\uDD16\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-800\",\n                                        children: \"AI 匹配分析\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setShowDetails(!showDetails),\n                                className: \"w-full text-left justify-start p-0 h-auto font-normal text-gray-600 hover:text-gray-800\",\n                                children: showDetails ? '收起分析' : '展开查看详细分析'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            showDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-white/70 rounded-md text-sm text-gray-700 leading-relaxed\",\n                                children: match.aiAnalysis.explanation\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                        href: \"/match/\".concat(match.id),\n                        className: \"block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"w-full flex items-center gap-2 bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 border-purple-200 hover:border-purple-300 transition-all duration-300 h-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5 text-purple-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-purple-700 font-medium\",\n                                    children: \"查看完整AI分析\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3 pt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"lg\",\n                                onClick: ()=>onPass(match.id),\n                                className: \"flex-1 flex items-center justify-center gap-2 text-gray-600 hover:text-red-600 hover:border-red-300 hover:bg-red-50 transition-all duration-300 h-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"跳过\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"lg\",\n                                onClick: ()=>onLike(match.id),\n                                className: \"flex-1 flex items-center justify-center gap-2 bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 shadow-lg hover:shadow-xl transition-all duration-300 h-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"喜欢\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/components/MatchCard.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(MatchCard, \"n2rC7YX8Mzz154E9USQBvseY7a0=\");\n_c = MatchCard;\nvar _c;\n$RefreshReg$(_c, \"MatchCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MatchCard.tsx\n"));

/***/ })

});