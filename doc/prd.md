# 产品描述文档：灵犀 AI (Lingxi AI)

**版本：** 1.0
**日期：** 2025年5月20日
**负责人：** [填写你的名字/团队名]

## 1. 引言 (Introduction)

* **项目名称：** 灵犀 AI (Lingxi AI)
* **项目愿景：** 通过深度 AI 模拟交互，重塑线上社交的匹配体验，帮助用户发现真正灵魂契合的伙伴。
* **核心价值：** 旨在解决传统社交应用中普遍存在的“滑动疲劳”、低效沟通和肤浅匹配的问题。灵犀 AI 提供一种更注重内在连接和个性化深度体验的社交方式，利用人工智能的力量，在用户进行真实接触前，预先模拟和评估潜在的契合度。我们相信，有意义的连接始于深度的相互理解，而 AI 可以成为促成这种理解的桥梁。

## 2. 目标用户 (Target Audience)

* **核心人群：** 对当前主流“快餐式”在线交友应用感到不满或疲惫，寻求更深层次、更有意义情感连接的年轻至中年用户群体（例如 22-40岁）。
* **技术接受度：** 对新兴技术，特别是人工智能在改善人际交往方面的应用持开放和积极态度的用户。
* **社交动机：** 倾向于投入时间进行高质量的匹配，而非追求即时满足感和海量选择的用户。
* **隐私关注：** 重视个人隐私，希望在建立初步信任和了解之前，不必过早暴露过多个人敏感信息的用户。
* **生活状态：** 可能是学生、年轻职场人士或有一定阅历的单身人士，他们有明确的社交意愿，但可能缺乏有效途径或时间去筛选和建立深度关系。

## 3. 核心功能 (Key Features)

### 3.1 用户个性化资料 (User Personalized Profile)
* **描述：** 用户在首次进入应用并完成注册后，将被引导创建一个详细的个性化资料。这份资料不仅是传统意义上的信息展示，更是 AI 理解用户、构建其虚拟人格（AI Agent）的基础。
* **细节：**
    * **基础信息：** 包括昵称、年龄、性别、所在城市等，用于基本匹配筛选。
    * **自我描述（核心）：** 提供一个引导性的开放式文本区域，鼓励用户深入描述自己的性格特点、价值观、人生哲学、生活方式、兴趣爱好、对理想伴侣的期望以及不希望遇到的特质等。AI 将重点分析此部分内容。
    * **兴趣标签：** 用户可以选择一系列预设的兴趣标签（如电影、音乐、户外运动、旅行、阅读、科技、艺术等），也可自定义添加，便于初步筛选和生成对话场景。
    * **照片上传：** 允许用户上传少量（例如1-3张）能代表个人风格和真实面貌的照片，这些照片在AI模拟阶段不会直接展示给对方用户，仅用于用户身份的辅助确认或在双方同意连接后展示。

### 3.2 AI 驱动的匹配引擎 (AI-Powered Matching Engine)
* **描述：** 当用户完成资料设置并主动发起匹配请求后，灵犀 AI 的智能匹配引擎将启动。此引擎的核心任务是在用户库中搜寻与请求者具有较高潜在契合度的其他用户。
* **细节：**
    * **用户发起：** 用户在主界面点击“开始AI匹配”按钮，系统将其加入待匹配队列。
    * **多维度分析：** AI 不仅基于基础信息和兴趣标签，更会深入分析自我描述中的语义信息，提取性格特质、价值观、生活态度等深层特征。
    * **潜在匹配池：** 根据分析结果，筛选出一个或多个潜在的匹配对象。初期可能侧重于寻找“互补性”或“相似性”较为明显的组合，后续算法会持续优化。
    * **匹配队列与时长：** 明确告知用户匹配过程需要一定时间（例如几小时到一天），管理用户预期。

### 3.3 AI 人格摘要生成 (AI Persona Brief Generation)
* **描述：** 一旦匹配引擎锁定一对潜在匹配用户（例如用户A和用户B），系统将首先为双方分别生成一份“AI人格摘要”。这是一个创新的中间步骤，旨在为后续的对话模拟提供更稳定、更聚焦的角色设定。
* **细节：**
    * **独立AI调用:** 系统会分别为用户A和用户B发起一次独立的AI调用。
    * **深度资料分析:** AI基于每位用户的完整资料（自我描述、兴趣、价值观及“认可”数据），提炼并生成一份结构化的“AI人格摘要”。
    * **摘要内容:** 这份摘要如同一个“角色设定卡”，精确描述了该用户的沟通风格、核心特质、潜在的关注点和对话中可能表现出的倾向，作为后续AI生成对话的“剧本大纲”。

### 3.4 整合式AI模拟与策划 (Integrated AI Simulation & Planning)
* **描述：** 在获得双方的“AI人格摘要”后，系统将通过一次统一的AI调用，高效地生成模拟对话、匹配评估和约会计划。
* **细节：**
    * **整合输入:** 系统将用户A和用户B的“AI人格摘要”、以及根据双方共同点生成的“智能对话场景”（例如：“在一个雨天的午后咖啡馆，两人因同一本书不期而遇”）打包，作为完整的上下文，发起一次整合的AI调用。
    * **复合任务指令:** 在这次调用中，AI被赋予一个复合指令，要求它一次性完成以下所有任务：
        * **多轮对话模拟:** 基于两份精确的“人格摘要”，在指定场景下生成一段限定轮次（例如 5-10 轮）的、高质量且符合人设的模拟对话。
        * **对话分析与契合度评估:** 对话生成后，立即对内容进行分析，从关键主题、情感倾向、沟通流畅度、价值观展现等方面，评估双方的契合度，并给出一个综合判断等级（例如：高度推荐、值得尝试）。
        * **个性化约会计划生成:** 如果契合度达到某一阈值，AI将继续基于双方的兴趣和地理位置信息，生成一份个性化的初次约会计划建议。
        * **统一输出与记录:** 整个生成过程的最终产物（模拟对话、评估总结、约会计划）将被打包并完整记录，供用户后续回顾。

### 3.5 匹配回顾与双向确认 (Match Review & Mutual Confirmation)
* **描述：** AI 完成评估和策划后，会通知双方用户（用户A和用户B）匹配已准备就绪。用户可以详细查看此次 AI 模拟的全部成果，并基于此独立做出是否愿意与对方建立真实连接的决定。
* **细节：**
    * **AI Agent 对话回放：** 用户可以完整地、像阅读剧本一样回顾两个 AI Agent 的所有对话内容。界面会清晰标识哪个 Agent 代表自己，哪个代表对方。
    * **AI 生成约会计划查看：** 用户可以查看 AI 推荐的约会地点、时间及活动建议。
    * **独立决策：** 双方用户在各自的界面上，独立地选择“同意连接”或“再看看”（或其他类似选项）。此过程互不可见，避免压力。

### 3.6 安全连接 (Secure Connection)
* **描述：** 只有在双方用户都对同一次 AI 模拟匹配结果表示“同意连接”后，系统才会为他们开启进一步真实联系的通道。
* **细节：**
    * **双向同意机制：** 必须用户A和用户B均选择“同意连接”。
    * **信息交换/功能解锁：** 连接成功后，根据应用的具体设计，可能会：
        * 解锁应用内建的安全聊天功能。
        * 交换双方在注册时预留且同意在此阶段分享的联系方式（例如加密处理的邮箱、或一个特定的社交媒体账号ID）。
    * **单方或双方拒绝：** 若任何一方选择“再看看”，则此次匹配结束，双方的真实信息不会有任何形式的互通。用户可以等待下一次匹配。

### 3.7 AI Agent 学习与进化 (AI Agent Learning & Evolution)
* **描述：** 这是灵犀 AI 的一个核心成长机制。用户在回顾自己的 AI Agent 的发言时，如果认为某句（或某段）表达特别符合自己的真实想法或沟通风格，可以进行“认可”并保存。
* **细节：**
    * **“认可”功能：** 在对话回放界面，用户可以对代表自己的 AI Agent 的每一条发言旁边的“认可”按钮（例如一个心形或大拇指图标）进行点击。
    * **话题/表达风格保存：** 被“认可”的对话片段、核心观点或表达方式，将被结构化地存储到该用户的个人偏好数据库中。
    * **Agent 持续优化：** 这些积累的“认可数据”将成为未来生成该用户 AI Agent 时最重要的参考资料之一。AI 会学习这些偏好，使得后续生成的 Agent 在表达和互动时越来越贴近用户本人的真实个性和风格，从而提高模拟的准确性和匹配的成功率。

## 4. 用户流程 (User Flow)

### 4.1 整体用户旅程 (Overall User Journey)
```mermaid
graph TD
    A[用户下载/打开App] --> B(注册/登录);
    B --> C{新用户?};
    C -- 是 --> D["创建个性化资料 (3.1)"];
    C -- 否 --> E[主界面];
    D --> E;
    E --> F["发起AI匹配请求 (3.2)"];
    F -- 等待 --> G["AI匹配与模拟进行中... (3.3)"];
    G --> H{"AI评估与计划完成 (3.4)"};
    H -- 通知 --> I["查看匹配回顾 (对话/约会计划) (3.5)"];
    I -- 查看对话 --> M["认可Agent对话片段 (3.7)"];
    M --> N(AI Agent资料库更新);
    I -- 决策 --> J{双方均同意连接?};
    J -- 是 --> K["连接成功/交换联系方式 (3.6)"];
    J -- 否 (一方或双方拒绝) --> E;
    K --> L["用户间真实互动 (App外或App内)"];
    E --> O[管理个人资料/查看认可话题/设置];
```

### 4.2 AI 匹配及模拟互动核心流程 (Core AI Matching & Simulation Flow)
```mermaid
graph TD
    subgraph AI Matching & Simulation Engine
        direction LR
        P1[用户A发起匹配请求] --> P2{在用户库中寻找潜在用户B};
        P2 -- 筛选成功 --> P3[加载用户A、B完整资料];
        P3 --> P4["AI调用: 为用户A生成人格摘要 (3.3)"];
        P3 --> P5["AI调用: 为用户B生成人格摘要 (3.3)"];
        P4 -- 人格摘要A --> P6;
        P5 -- 人格摘要B --> P6;
        P6{整合人格摘要A、B和对话场景};
        P6 --> P7["整合式AI调用 (3.4)<br/>生成对话、评估与计划"];
        P7 --> P8{评估结果积极?};
        P8 -- 是 --> P9["打包(对话+评估+计划) -> 通知用户回顾"];
        P8 -- 否 --> P10[记录尝试，为用户A寻找新匹配];
        P10 --> P2;
    end
```

## 5. 预期目标与成功指标 (Goals & Success Metrics)
- 用户增长与活跃度：
    - 注册用户数 (Total Registered Users): 应用的总用户规模。
    - 月活跃用户数 (MAU - Monthly Active Users): 应用的健康度和用户粘性。
    - 日活跃用户数 (DAU - Daily Active Users): 更高频的用户参与度。
- 匹配质量与效率：
    - 匹配成功率 (Match Success Rate): （双方同意连接的匹配数 / 发起AI模拟的总匹配数）* 100%。
    - AI Agent 认可率 (Agent Endorsement Rate): 用户“认可”其 AI Agent 发言的平均次数或比例。
    - 平均匹配时长 (Average Time to Match): 从用户发起匹配到收到回顾通知的平均时间。
    - 转化至线下率 (Offline Meeting Conversion - 长期追踪): 成功连接的用户中，实际进行线下见面的比例（可通过匿名调研收集）。
- 用户满意度与留存：
    - 应用商店评分 (App Store Ratings): 直接的用户反馈。
    - 用户调研/NPS (Net Promoter Score): 用户推荐意愿和整体满意度。
    - 用户留存率 (User Retention Rate): 例如次日留存、7日留存、30日留存，特别是完成一次完整匹配流程（无论成功与否）后的用户留存。
    - 功能使用率 (Feature Adoption Rate): 各核心功能（如发起匹配、认可话题等）的使用频率。
- AI 性能与准确性：
    - AI Agent 生成耗时 (Agent Generation Time): 服务器处理效率。
    - 模拟对话质量 (Simulated Conversation Quality): 通过人工抽样评估对话的自然度、深度、与用户资料的符合程度。
    - 约会计划采纳率 (Date Plan Acceptance Rate): 若双方同意连接，他们对AI生成的约会计划的满意度或实际采纳情况。

## 6. 未来展望 (Future Considerations)
- 应用内安全即时通讯： 在双方用户同意连接后，提供端到端加密的 App 内聊天功能，保护用户在初识阶段的隐私。
- AI Agent 多模态交互：
    - 声音模拟： 允许用户录制一小段声音，AI Agent 可以用类似的声音进行模拟对话（用户可选）。
    - 更丰富的个性化维度： 引入更多心理学维度的测试或选项，让 AI Agent 的人格更丰满。
    - 动态互动场景扩展： AI Agent 不仅限于文本对话，还可以参与一些简单的选择型互动小游戏或共同决策模拟，以更动态的方式展现性格和偏好。
    - 智能破冰与话题引导： 在真实聊天阶段，AI 可根据双方的共同点和此前的模拟对话，提供一些个性化的破冰话题或聊天建议。
- 社群与活动： 基于用户的共同兴趣或匹配成功的经验，尝试组织小范围的线上或线下主题社群及活动（需严格保护隐私）。
- 高级订阅服务 (Monetization)：
    - 加速匹配： 优先处理匹配请求。
    - 每日更多匹配机会。
    - 高级 AI Agent 定制选项。
    - 深度匹配报告解读。
- 全球化与多语言支持： 将应用推广到更多国家和地区，支持多种语言。
- 持续的伦理与偏见审查： 定期审查 AI 算法，确保匹配的公平性，避免因数据或算法设计带来的偏见。

## 7. 名词解释 (Glossary)
- AI Agent： 指在“灵犀 AI”应用中，由人工智能根据真实用户的个人资料和偏好创建的虚拟人格。该虚拟人格在模拟环境中代表用户进行互动和对话，以探索与另一用户的潜在契合度。
- 认可话题/认可表达： 指用户在回顾其 AI Agent 的模拟对话时，主动标记的、认为非常符合自身真实想法、价值观或沟通风格的对话片段、核心观点或具体表达方式。这些被“认可”的内容将用于优化该用户未来的 AI Agent 生成。
- 平铺展示 (Prototyping Term)： 在本产品原型设计阶段特指的一种展示方式，即将应用的所有主要界面（或界面片段）在同一个主页面（如 index.html）中，通过 iframe 嵌入并按顺序纵向排列显示，方便一次性概览所有设计。
- 双向确认 (Mutual Confirmation)： 指在 AI 完成匹配模拟后，必须参与匹配的双方用户都独立地对该次匹配结果表示“同意连接”，系统才会认为连接成功并开启后续交互的机制。